'use client';

import { useState, useCallback } from 'react';

import { toast } from 'src/components/snackbar';

import { callRPC, fetchData } from './supabase-utils';
import {
  validateOrderStatusTransition,
  getNextValidStatuses as getNextValidStatusesFromRules,
  getBusinessFlow
} from './order-status-business-rules';
import {
  initializeOrderWorkflow,
  transitionOrderWorkflow,
  getNextWorkflowStatuses
} from './workflow-order-integration';

// ==========================================
// 📊 ENHANCED ORDER STATUS MANAGEMENT
// ==========================================

/**
 * Enhanced Order Service với tính năng tối ưu
 * - Tự động tracking kho hàng
 * - Cập nhật trạng thái thông minh
 * - <PERSON>ồng bộ hệ thống toàn diện
 * - <PERSON><PERSON><PERSON> sử đơn hàng chi tiết
 */

// Constants
const ORDERS_TABLE = 'orders';

// Enhanced Order Status với business type support
export const ENHANCED_ORDER_STATUS = {
  // Common statuses
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  PROCESSING: 'processing',
  PAID: 'paid',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded',

  // Physical products (retail)
  PACKAGING: 'packaging',
  SHIPPING: 'shipping',
  DELIVERED: 'delivered',

  // Digital products
  PREPARING: 'preparing',
  READY_DOWNLOAD: 'ready_download',
  SENT: 'sent',

  // Services
  SCHEDULING: 'scheduling',
  IN_PROGRESS: 'in_progress',
  PROVIDED: 'provided',
};

// Business type specific status flows - Đồng bộ với database và ORDER_STATUS_OPTIONS
export const STATUS_FLOWS = {
  // Sản phẩm vật lý (Simple products)
  simple: [
    'pending', 'confirmed', 'processing', 'paid',
    'packaging', 'shipping', 'delivered', 'completed'
  ],
  // Sản phẩm có biến thể (Variable products)
  variable: [
    'pending', 'confirmed', 'processing', 'paid',
    'packaging', 'shipping', 'delivered', 'completed'
  ],
  // Sản phẩm số (Digital products)
  digital: [
    'pending', 'confirmed', 'processing', 'paid',
    'preparing', 'ready_download', 'sent', 'completed'
  ],
  // Dịch vụ (Services)
  service: [
    'pending', 'confirmed', 'processing', 'paid',
    'scheduling', 'in_progress', 'provided', 'completed'
  ],
  // Hybrid business (Retail + Digital + Service)
  hybrid: [
    'pending', 'confirmed', 'processing', 'paid',
    'packaging', 'shipping', 'delivered', 'completed'
  ],
  // Backward compatibility
  retail: [
    'pending', 'confirmed', 'processing', 'paid',
    'packaging', 'shipping', 'delivered', 'completed'
  ],
  services: [
    'pending', 'confirmed', 'processing', 'paid',
    'scheduling', 'in_progress', 'provided', 'completed'
  ]
};

// Inventory impact by status
export const INVENTORY_IMPACT = {
  confirmed: 'reserve',    // Đặt trước
  cancelled: 'release',    // Giải phóng
  refunded: 'release',     // Giải phóng
  completed: 'confirm',    // Xác nhận xuất kho
};

/**
 * 🚀 ENHANCED ORDER STATUS UPDATE
 * Cập nhật trạng thái đơn hàng với tự động tracking kho hàng
 * Hỗ trợ cả custom workflow và legacy hardcoded flows
 */
export async function updateOrderStatusEnhanced(orderId, newStatus, options = {}) {
  try {
    const {
      comment = '',
      userId = null,
      autoInventoryUpdate = true,
      businessType = 'retail',
      notifyCustomer = true,
      useWorkflow = true // Mặc định sử dụng custom workflow
    } = options;

    // Try custom workflow first if enabled
    if (useWorkflow) {
      try {
        const workflowResult = await transitionOrderWorkflow(orderId, newStatus, {
          userId,
          comment,
          triggerType: 'manual',
          autoInventoryUpdate
        });

        if (workflowResult.success) {
          return {
            success: true,
            data: workflowResult.data,
            message: workflowResult.message,
            source: 'workflow'
          };
        } else {
          console.warn('Workflow transition failed, falling back to legacy system:', workflowResult.error);
        }
      } catch (workflowError) {
        console.warn('Workflow system error, falling back to legacy system:', workflowError.message);
      }
    }

    // Legacy system (fallback or when useWorkflow = false)
    // 1. Lấy thông tin đơn hàng hiện tại
    const orderResult = await fetchData(ORDERS_TABLE, {
      filters: { id: orderId },
      select: '*'
    });

    if (!orderResult.success || !orderResult.data?.length) {
      throw new Error('Không tìm thấy đơn hàng');
    }

    const currentOrder = orderResult.data[0];
    const previousStatus = currentOrder.status;

    // 2. Validate status transition using business rules
    const isValidTransition = validateOrderStatusTransition(previousStatus, newStatus, businessType, {
      allowSkipSteps: autoInventoryUpdate, // Cho phép skip steps nếu có auto inventory
      maxSkipSteps: 2
    });
    if (!isValidTransition.valid) {
      throw new Error(isValidTransition.message);
    }

    // 3. Lấy danh sách items của đơn hàng


    // 4. Sử dụng database function để cập nhật trạng thái và xử lý inventory
    const dbResult = await callRPC('update_order_status_with_history', {
      p_order_id: orderId,
      p_new_status: newStatus,
      p_previous_status: previousStatus,
      p_comment: comment,
      p_user_id: userId,
      p_auto_inventory_update: autoInventoryUpdate
    });

    if (!dbResult.success) {
      throw new Error(dbResult.error || 'Không thể cập nhật trạng thái đơn hàng');
    }

    // 5. Gửi thông báo cho khách hàng (nếu cần)
    if (notifyCustomer) {
      await sendCustomerNotification(currentOrder, newStatus, previousStatus);
    }

    // 6. Trigger automation rules
    await triggerOrderAutomation(orderId, newStatus, previousStatus, currentOrder);

    return {
      success: true,
      data: {
        orderId,
        previousStatus: dbResult.data?.previous_status || previousStatus,
        newStatus: dbResult.data?.new_status || newStatus,
        inventoryUpdated: dbResult.data?.inventory_updated || autoInventoryUpdate,
        affectedItems: dbResult.data?.affected_items || 0,
        historyCreated: true,
        notificationSent: notifyCustomer
      },
      message: dbResult.data?.message || `Đã cập nhật trạng thái đơn hàng từ "${getStatusLabel(previousStatus)}" thành "${getStatusLabel(newStatus)}"`,
      source: 'legacy'
    };

  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi cập nhật trạng thái đơn hàng'
    };
  }
}


// validateStatusTransition đã được chuyển sang order-status-business-rules.js


/**
 * 🏷️ GET STATUS LABEL
 * Lấy nhãn hiển thị cho trạng thái - Đồng bộ với ORDER_STATUS_OPTIONS
 */
function getStatusLabel(status) {
  // Import từ constants để đảm bảo đồng bộ
  const { getOrderStatusLabel } = require('./order-constants');
  return getOrderStatusLabel(status);
}




/**
 * 📧 SEND CUSTOMER NOTIFICATION
 * Gửi thông báo cho khách hàng
 */
async function sendCustomerNotification(order, newStatus, previousStatus) {
  try {
    // Implement notification logic here
    // Có thể gửi email, SMS, hoặc push notification

    const notificationData = {
      orderId: order.id,
      orderNumber: order.order_number,
      customerEmail: order.customer_email,
      customerPhone: order.customer_phone,
      newStatus,
      previousStatus,
      statusLabel: getStatusLabel(newStatus)
    };
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * ⚡ TRIGGER ORDER AUTOMATION
 * Kích hoạt các quy tắc tự động hóa
 */
async function triggerOrderAutomation(orderId, newStatus, previousStatus, orderData) {
  try {
    // Implement automation rules trigger
    // Có thể gọi automation service hoặc webhook

    const automationData = {
      trigger: 'order_status_changed',
      orderId,
      newStatus,
      previousStatus,
      orderData,
      timestamp: new Date().toISOString()
    };
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * 🎯 BATCH ORDER STATUS UPDATE
 * Cập nhật trạng thái hàng loạt
 */
export async function batchUpdateOrderStatus(orderIds, newStatus, options = {}) {
  try {
    const {
      comment = '',
      userId = null,
      autoInventoryUpdate = true,
      businessType = 'retail'
    } = options;

    const results = [];
    const errors = [];

    for (const orderId of orderIds) {
      try {
        const result = await updateOrderStatusEnhanced(orderId, newStatus, {
          comment,
          userId,
          autoInventoryUpdate,
          businessType,
          notifyCustomer: false, // Không gửi thông báo cho batch update
          createHistory: true
        });

        if (result.success) {
          results.push({ orderId, success: true, data: result.data });
        } else {
          errors.push({ orderId, error: result.error });
        }
      } catch (error) {
        errors.push({ orderId, error: error.message });
      }
    }

    return {
      success: errors.length === 0,
      data: {
        totalOrders: orderIds.length,
        successCount: results.length,
        errorCount: errors.length,
        results,
        errors
      },
      message: `Đã cập nhật ${results.length}/${orderIds.length} đơn hàng thành công`
    };

  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi cập nhật hàng loạt'
    };
  }
}

/**
 * 📈 GET ORDER ANALYTICS
 * Lấy thống kê đơn hàng theo trạng thái với đồng bộ tenant_id
 */
export async function getOrderAnalytics(options = {}) {
  try {
    const {
      dateFrom = null,
      dateTo = null
    } = options;

    // Sử dụng supabase-utils để tự động xử lý tenant_id
    let filters = {};

    // Xử lý filter cho created_at với range - sử dụng tên field chính xác
    if (dateFrom || dateTo) {
      filters.created_at = {};

      if (dateFrom) {
        // Đảm bảo format ISO string - không thay đổi timezone
        const fromDate = typeof dateFrom === 'string' ? new Date(dateFrom) : dateFrom;

      }

      if (dateTo) {
        // Đảm bảo format ISO string - không thay đổi timezone
        const toDate = typeof dateTo === 'string' ? new Date(dateTo) : dateTo;
        // Không set hours nữa vì đã được set từ client
        filters.created_at.lte = toDate.toISOString();
      }
    }

    const ordersResult = await fetchData(ORDERS_TABLE, {
      filters,
      select: 'id, status, total_amount, created_at, order_number, customer_name'
    });

    if (!ordersResult.success) {
      throw new Error(`Không thể lấy dữ liệu đơn hàng: ${ordersResult.error?.message || ordersResult.error}`);
    }

    const orders = ordersResult.data || [];

    // Group by status với xử lý an toàn và field mapping chính xác
    const analytics = orders.reduce((acc, order) => {
      const status = order.status || 'pending';
      if (!acc[status]) {
        acc[status] = {
          count: 0,
          totalAmount: 0,
          orders: []
        };
      }

      acc[status].count += 1;
      // Sử dụng field đã được chuyển đổi từ supabase-utils (totalAmount thay vì total_amount)
      const orderAmount = parseFloat(order.totalAmount || order.total_amount || 0);
      acc[status].totalAmount += orderAmount;
      acc[status].orders.push(order);

      return acc;
    }, {});

    // Calculate totals với xử lý an toàn và field mapping chính xác
    const totalAmount = orders.reduce((sum, order) => {
      const orderAmount = parseFloat(order.totalAmount || order.total_amount || 0);
      return sum + orderAmount;
    }, 0);

    const totals = {
      totalOrders: orders.length,
      totalAmount,
      averageOrderValue: orders.length > 0 ? totalAmount / orders.length : 0
    };

    return {
      success: true,
      data: {
        analytics,
        totals,
        period: { dateFrom, dateTo },
        generatedAt: new Date().toISOString(),
        orderCount: orders.length
      }
    };

  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi lấy thống kê đơn hàng'
    };
  }
}

/**
 * 🎣 REACT HOOK FOR ENHANCED ORDER MANAGEMENT
 * Hook để sử dụng enhanced order service trong React components
 */
export function useEnhancedOrderService() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Update single order status
  const updateStatus = useCallback(async (orderId, newStatus, options = {}) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await updateOrderStatusEnhanced(orderId, newStatus, options);

      if (result.success) {
        toast.success(result.message);
        return result;
      } else {
        setError(result.error);
        toast.error(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = err.message || 'Lỗi khi cập nhật trạng thái đơn hàng';
      setError(errorMessage);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Batch update order status
  const batchUpdateStatus = useCallback(async (orderIds, newStatus, options = {}) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await batchUpdateOrderStatus(orderIds, newStatus, options);

      if (result.success) {
        toast.success(result.message);
        return result;
      } else {
        setError(result.error);
        toast.error(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = err.message || 'Lỗi khi cập nhật hàng loạt';
      setError(errorMessage);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Get order analytics - Memoized để tránh re-render
  const getAnalytics = useCallback(async (options = {}) => {
    try {
      setError(null);

      const result = await getOrderAnalytics(options);

      if (!result.success) {
        setError(result.error);
      }

      return result;
    } catch (err) {
      const errorMessage = err.message || 'Lỗi khi lấy thống kê đơn hàng';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, []); // Không phụ thuộc vào isLoading để tránh circular dependency

  // Get next valid statuses for a given current status and business type (SYNC VERSION)
  // For UI components that need immediate response
  const getNextValidStatuses = useCallback((currentStatus, businessType = 'simple') => {
    return getNextValidStatusesFromRules(currentStatus, businessType, {
      allowSkipSteps: true,
      maxSkipSteps: 2,
      includeCancellation: true
    });
  }, []);

  // Get next valid statuses with workflow support (ASYNC VERSION)
  // For advanced workflow integration
  const getNextValidStatusesWithWorkflow = useCallback(async (currentStatus, businessType = 'simple', orderId = null) => {
    // Try to get next statuses from workflow if orderId is provided
    if (orderId) {
      try {
        const workflowResult = await getNextWorkflowStatuses(orderId);
        if (workflowResult.success && workflowResult.data.length > 0) {
          return workflowResult.data;
        }
      } catch (error) {
        console.warn('Failed to get workflow statuses, falling back to rules:', error.message);
      }
    }

    // Fallback to rule-based statuses
    return getNextValidStatusesFromRules(currentStatus, businessType, {
      allowSkipSteps: true,
      maxSkipSteps: 2,
      includeCancellation: true
    });
  }, []);

  // Get status color for UI display - Đồng bộ với ORDER_STATUS_OPTIONS
  const getStatusColor = useCallback((status) => {
    const { getOrderStatusColor } = require('./order-constants');
    return getOrderStatusColor(status);
  }, []);

  return {
    // Actions
    updateStatus,
    batchUpdateStatus,
    getAnalytics,

    // Utilities
    getNextValidStatuses, // Sync version for UI components
    getNextValidStatusesWithWorkflow, // Async version with workflow support
    getStatusColor,
    getStatusLabel,

    // State
    isLoading,
    error,

    // Constants
    ORDER_STATUS: ENHANCED_ORDER_STATUS,
    STATUS_FLOWS
  };
}