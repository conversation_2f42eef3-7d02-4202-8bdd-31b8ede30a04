'use client';

/**
 * <PERSON><PERSON><PERSON> hằng số và cấu hình cho module đơn hàng
 */

// Tên bảng trong Supabase
export const TABLE_NAME = 'orders';
export const ORDER_ITEMS_TABLE = 'order_items';
export const ORDER_STATUSES_TABLE = 'order_statuses';
export const ORDER_HISTORY_TABLE = 'order_history';
export const CUSTOMERS_TABLE = 'customers';
export const CUSTOMER_ADDRESSES_TABLE = 'customer_addresses';

// Cấu hình mặc định
export const DEFAULT_ORDER_OPTIONS = {
  orderBy: 'createdAt',
  ascending: false,
};

// Trạng thái đơn hàng - Tối ưu cho doanh nghiệp
export const ORDER_STATUS = {
  // Quy trình chính
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  PAID: 'paid',
  PROCESSING: 'processing',
  SHIPPING: 'shipping',
  DELIVERED: 'delivered',
  COMPLETED: 'completed',

  // Trạng thái đặc biệt
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded',
};

// Phương thức thanh toán
export const PAYMENT_METHODS = {
  COD: 'cod',
  BANK_TRANSFER: 'bank_transfer',
  CREDIT_CARD: 'credit_card',
  MOMO: 'momo',
  ZALOPAY: 'zalopay',
};

// Phương thức vận chuyển
export const SHIPPING_METHODS = {
  STANDARD: 'standard',
  EXPRESS: 'express',
  FREE: 'free',
};

// Tên trường trong database - đồng bộ chính xác với schema DB
export const DB_FIELDS = {
  ID: 'id',
  STORE_ID: 'storeId',
  CUSTOMER_ID: 'customerId',
  STATUS: 'status',
  ORDER_NUMBER: 'orderNumber',
  TOTAL_AMOUNT: 'totalAmount',
  SUBTOTAL: 'subtotal',
  SHIPPING_AMOUNT: 'shippingAmount',
  TAX_AMOUNT: 'taxAmount',
  DISCOUNT_AMOUNT: 'discountAmount',
  SHIPPING_ADDRESS_ID: 'shippingAddressId',
  BILLING_ADDRESS_ID: 'billingAddressId',
  SHIPPING_METHOD: 'shippingMethod',
  PAYMENT_METHOD: 'paymentMethod',
  NOTES: 'notes',
  CUSTOMER_EMAIL: 'customerEmail',
  CUSTOMER_PHONE: 'customerPhone',
  CUSTOMER_NAME: 'customerName',
  CREATED_AT: 'createdAt',
  UPDATED_AT: 'updatedAt',
};

// Tên trường trong UI form
export const FORM_FIELDS = {
  ORDER_NUMBER: 'orderNumber',
  CUSTOMER_ID: 'customerId',
  STATUS: 'status',
  TOTAL_AMOUNT: 'totalAmount',
  SUBTOTAL: 'subtotal',
  SHIPPING_AMOUNT: 'shippingAmount',
  TAX_AMOUNT: 'taxAmount',
  DISCOUNT_AMOUNT: 'discountAmount',
  SHIPPING_ADDRESS_ID: 'shippingAddressId',
  BILLING_ADDRESS_ID: 'billingAddressId',
  SHIPPING_METHOD: 'shippingMethod',
  PAYMENT_METHOD: 'paymentMethod',
  NOTES: 'notes',
  CUSTOMER_EMAIL: 'customerEmail',
  CUSTOMER_PHONE: 'customerPhone',
  CUSTOMER_NAME: 'customerName',
  ORDER_ITEMS: 'orderItems',
};

// Mapping giữa trường form và trường database
export const FORM_TO_DB_MAPPING = {
  [FORM_FIELDS.ORDER_NUMBER]: DB_FIELDS.ORDER_NUMBER,
  [FORM_FIELDS.CUSTOMER_ID]: DB_FIELDS.CUSTOMER_ID,
  [FORM_FIELDS.STATUS]: DB_FIELDS.STATUS,
  [FORM_FIELDS.TOTAL_AMOUNT]: DB_FIELDS.TOTAL_AMOUNT,
  [FORM_FIELDS.SUBTOTAL]: DB_FIELDS.SUBTOTAL,
  [FORM_FIELDS.SHIPPING_AMOUNT]: DB_FIELDS.SHIPPING_AMOUNT,
  [FORM_FIELDS.TAX_AMOUNT]: DB_FIELDS.TAX_AMOUNT,
  [FORM_FIELDS.DISCOUNT_AMOUNT]: DB_FIELDS.DISCOUNT_AMOUNT,
  [FORM_FIELDS.SHIPPING_ADDRESS_ID]: DB_FIELDS.SHIPPING_ADDRESS_ID,
  [FORM_FIELDS.BILLING_ADDRESS_ID]: DB_FIELDS.BILLING_ADDRESS_ID,
  [FORM_FIELDS.SHIPPING_METHOD]: DB_FIELDS.SHIPPING_METHOD,
  [FORM_FIELDS.PAYMENT_METHOD]: DB_FIELDS.PAYMENT_METHOD,
  [FORM_FIELDS.NOTES]: DB_FIELDS.NOTES,
  [FORM_FIELDS.CUSTOMER_EMAIL]: DB_FIELDS.CUSTOMER_EMAIL,
  [FORM_FIELDS.CUSTOMER_PHONE]: DB_FIELDS.CUSTOMER_PHONE,
  [FORM_FIELDS.CUSTOMER_NAME]: DB_FIELDS.CUSTOMER_NAME,
};

/**
 * Các tùy chọn trạng thái đơn hàng - Đầy đủ đồng bộ với database
 *
 * Đồng bộ hoàn toàn với:
 * - Database enum order_status_enum (16 trạng thái)
 * - Bảng order_statuses với product_type support
 * - Business flows cho từng loại sản phẩm
 */
export const ORDER_STATUS_OPTIONS = [
  // === QUY TRÌNH CHUNG - Áp dụng cho tất cả loại sản phẩm ===
  {
    value: 'pending',
    label: 'Chờ xác nhận',
    color: 'warning',
    hexColor: '#FF9800',
    productTypes: ['all'],
    sortOrder: 10,
    description: 'Đơn hàng mới được tạo, đang chờ xác nhận từ người bán'
  },
  {
    value: 'confirmed',
    label: 'Đã xác nhận',
    color: 'info',
    hexColor: '#2196F3',
    productTypes: ['all'],
    sortOrder: 20,
    description: 'Đơn hàng đã được xác nhận và sẽ được xử lý'
  },
  {
    value: 'processing',
    label: 'Đang xử lý',
    color: 'primary',
    hexColor: '#1976D2',
    productTypes: ['all'],
    sortOrder: 30,
    description: 'Đơn hàng đang được xử lý và chuẩn bị'
  },
  {
    value: 'paid',
    label: 'Đã thanh toán',
    color: 'success',
    hexColor: '#4CAF50',
    productTypes: ['all'],
    sortOrder: 40,
    description: 'Khách hàng đã thanh toán đầy đủ cho đơn hàng'
  },

  // === SẢN PHẨM VẬT LÝ (Simple & Variable) ===
  {
    value: 'packaging',
    label: 'Đang đóng gói',
    color: 'info',
    hexColor: '#00BCD4',
    productTypes: ['simple', 'variable'],
    sortOrder: 50,
    description: 'Sản phẩm đang được đóng gói để chuẩn bị giao hàng'
  },
  {
    value: 'shipping',
    label: 'Đang vận chuyển',
    color: 'primary',
    hexColor: '#3F51B5',
    productTypes: ['simple', 'variable'],
    sortOrder: 60,
    description: 'Đơn hàng đang được vận chuyển đến khách hàng'
  },
  {
    value: 'delivered',
    label: 'Đã giao hàng',
    color: 'success',
    hexColor: '#8BC34A',
    productTypes: ['simple', 'variable'],
    sortOrder: 70,
    description: 'Đơn hàng đã được giao thành công đến khách hàng'
  },

  // === SẢN PHẨM SỐ (Digital) ===
  {
    value: 'preparing',
    label: 'Đang chuẩn bị',
    color: 'info',
    hexColor: '#00BCD4',
    productTypes: ['digital'],
    sortOrder: 50,
    description: 'Sản phẩm số đang được chuẩn bị và xử lý'
  },
  {
    value: 'ready_download',
    label: 'Sẵn sàng tải xuống',
    color: 'success',
    hexColor: '#4CAF50',
    productTypes: ['digital'],
    sortOrder: 60,
    description: 'Sản phẩm số đã sẵn sàng để khách hàng tải xuống'
  },
  {
    value: 'sent',
    label: 'Đã gửi',
    color: 'success',
    hexColor: '#8BC34A',
    productTypes: ['digital'],
    sortOrder: 70,
    description: 'Sản phẩm số đã được gửi đến khách hàng'
  },

  // === DỊCH VỤ (Service) ===
  {
    value: 'scheduling',
    label: 'Đang lên lịch',
    color: 'warning',
    hexColor: '#FF9800',
    productTypes: ['service'],
    sortOrder: 50,
    description: 'Đang sắp xếp lịch trình để cung cấp dịch vụ'
  },
  {
    value: 'in_progress',
    label: 'Đang thực hiện',
    color: 'primary',
    hexColor: '#3F51B5',
    productTypes: ['service'],
    sortOrder: 60,
    description: 'Dịch vụ đang được thực hiện cho khách hàng'
  },
  {
    value: 'provided',
    label: 'Đã cung cấp',
    color: 'success',
    hexColor: '#8BC34A',
    productTypes: ['service'],
    sortOrder: 70,
    description: 'Dịch vụ đã được cung cấp hoàn tất cho khách hàng'
  },

  // === TRẠNG THÁI CUỐI ===
  {
    value: 'completed',
    label: 'Hoàn thành',
    color: 'success',
    hexColor: '#4CAF50',
    productTypes: ['all'],
    sortOrder: 100,
    description: 'Đơn hàng đã hoàn thành toàn bộ quy trình'
  },

  // === TRẠNG THÁI ĐẶC BIỆT ===
  {
    value: 'cancelled',
    label: 'Đã hủy',
    color: 'error',
    hexColor: '#F44336',
    productTypes: ['all'],
    sortOrder: 110,
    description: 'Đơn hàng đã bị hủy bỏ'
  },
  {
    value: 'refunded',
    label: 'Đã hoàn tiền',
    color: 'secondary',
    hexColor: '#9E9E9E',
    productTypes: ['all'],
    sortOrder: 120,
    description: 'Đơn hàng đã được hoàn tiền cho khách hàng'
  },
];

// Các tùy chọn phương thức thanh toán
export const PAYMENT_METHOD_OPTIONS = [
  { value: 'cod', label: 'Thanh toán khi nhận hàng (COD)' },
  { value: 'bank_transfer', label: 'Chuyển khoản ngân hàng' },
  { value: 'credit_card', label: 'Thẻ tín dụng/ghi nợ' },
  { value: 'momo', label: 'Ví MoMo' },
  { value: 'zalopay', label: 'ZaloPay' },
];

// Các tùy chọn phương thức vận chuyển
export const SHIPPING_METHOD_OPTIONS = [
  { value: 'standard', label: 'Vận chuyển tiêu chuẩn' },
  { value: 'express', label: 'Vận chuyển nhanh' },
  { value: 'free', label: 'Miễn phí vận chuyển' },
];

// =====================================================
// UTILITY FUNCTIONS CHO ORDER STATUS
// =====================================================

/**
 * Lấy danh sách trạng thái theo loại sản phẩm
 * @param {string} productType - Loại sản phẩm (simple, variable, digital, service, all)
 * @returns {Array} - Danh sách trạng thái phù hợp
 */
export function getOrderStatusesByProductType(productType = 'all') {
  return ORDER_STATUS_OPTIONS.filter(status =>
    status.productTypes.includes(productType) || status.productTypes.includes('all')
  ).sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
}

/**
 * Lấy thông tin trạng thái theo value
 * @param {string} statusValue - Giá trị trạng thái
 * @returns {Object|null} - Thông tin trạng thái hoặc null
 */
export function getOrderStatusInfo(statusValue) {
  return ORDER_STATUS_OPTIONS.find(status => status.value === statusValue) || null;
}

/**
 * Kiểm tra trạng thái có hợp lệ cho loại sản phẩm không
 * @param {string} statusValue - Giá trị trạng thái
 * @param {string} productType - Loại sản phẩm
 * @returns {boolean} - True nếu hợp lệ
 */
export function isValidStatusForProductType(statusValue, productType) {
  const statusInfo = getOrderStatusInfo(statusValue);
  if (!statusInfo) return false;

  return statusInfo.productTypes.includes(productType) || statusInfo.productTypes.includes('all');
}

/**
 * Lấy màu sắc của trạng thái
 * @param {string} statusValue - Giá trị trạng thái
 * @returns {string} - Màu sắc MUI
 */
export function getOrderStatusColor(statusValue) {
  const statusInfo = getOrderStatusInfo(statusValue);
  return statusInfo?.color || 'default';
}

/**
 * Lấy nhãn hiển thị của trạng thái
 * @param {string} statusValue - Giá trị trạng thái
 * @returns {string} - Nhãn hiển thị
 */
export function getOrderStatusLabel(statusValue) {
  const statusInfo = getOrderStatusInfo(statusValue);
  return statusInfo?.label || statusValue;
}
