'use client';

import { 
  ORDER_STATUS_OPTIONS, 
  getOrderStatusInfo,
  getOrderStatusesByProductType,
  isValidStatusForProductType 
} from './order-constants';

/**
 * =====================================================
 * ORDER STATUS BUSINESS RULES SERVICE
 * =====================================================
 * 
 * Quản lý tất cả business rules liên quan đến trạng thái đơn hàng:
 * - Validation chuyển đổi trạng thái
 * - Business flows theo product type
 * - Quy tắc inventory management
 * - Permission checks
 */

// Product type mapping cho backward compatibility
export const PRODUCT_TYPE_MAPPING = {
  retail: 'simple',
  digital: 'digital',
  services: 'service',
  service: 'service',
  hybrid: 'simple',
  // Direct mappings
  simple: 'simple',
  variable: 'variable'
};

// Business flows theo product type (đồng bộ với database)
export const BUSINESS_FLOWS = {
  simple: [
    'pending', 'confirmed', 'processing', 'paid',
    'packaging', 'shipping', 'delivered', 'completed'
  ],
  variable: [
    'pending', 'confirmed', 'processing', 'paid',
    'packaging', 'shipping', 'delivered', 'completed'
  ],
  digital: [
    'pending', 'confirmed', 'processing', 'paid',
    'preparing', 'ready_download', 'sent', 'completed'
  ],
  service: [
    'pending', 'confirmed', 'processing', 'paid',
    'scheduling', 'in_progress', 'provided', 'completed'
  ]
};

// Trạng thái có thể inventory impact
export const INVENTORY_IMPACT_STATUSES = {
  confirmed: 'reserve',    // Đặt trước kho
  cancelled: 'release',    // Giải phóng kho
  refunded: 'release',     // Giải phóng kho
  completed: 'confirm'     // Xác nhận xuất kho
};

// Trạng thái final (không thể chuyển sang trạng thái khác)
export const FINAL_STATUSES = ['completed', 'cancelled', 'refunded'];

// Trạng thái có thể hủy/hoàn tiền
export const CANCELLABLE_STATUSES = [
  'pending', 'confirmed', 'processing', 'paid',
  'packaging', 'preparing', 'scheduling'
];

// Product type labels tiếng Việt
export const PRODUCT_TYPE_LABELS = {
  simple: 'Sản phẩm đơn giản',
  variable: 'Sản phẩm có biến thể',
  digital: 'Sản phẩm số',
  service: 'Dịch vụ',
  all: 'Tất cả loại sản phẩm'
};

/**
 * Lấy business flow theo product type
 * @param {string} productType - Loại sản phẩm
 * @returns {Array} - Danh sách trạng thái theo thứ tự
 */
export function getBusinessFlow(productType) {
  const mappedType = PRODUCT_TYPE_MAPPING[productType] || productType;
  return BUSINESS_FLOWS[mappedType] || BUSINESS_FLOWS.simple;
}

/**
 * Validate chuyển đổi trạng thái đơn hàng
 * @param {string} currentStatus - Trạng thái hiện tại
 * @param {string} newStatus - Trạng thái mới
 * @param {string} productType - Loại sản phẩm
 * @param {Object} options - Tùy chọn bổ sung
 * @returns {Object} - Kết quả validation
 */
export function validateOrderStatusTransition(currentStatus, newStatus, productType = 'simple', options = {}) {
  const { allowSkipSteps = false, maxSkipSteps = 2 } = options;
  
  // Lấy business flow cho product type
  const flow = getBusinessFlow(productType);
  const currentIndex = flow.indexOf(currentStatus);
  const newIndex = flow.indexOf(newStatus);

  // === SPECIAL CASES ===
  
  // Cho phép chuyển về cancelled/refunded từ các trạng thái có thể hủy
  if (newStatus === 'cancelled' || newStatus === 'refunded') {
    if (!CANCELLABLE_STATUSES.includes(currentStatus)) {
      const actionText = newStatus === 'cancelled' ? 'hủy đơn hàng' : 'hoàn tiền';
      return {
        valid: false,
        message: `Không thể ${actionText} khi đơn hàng đang ở trạng thái "${getOrderStatusInfo(currentStatus)?.label}". Chỉ có thể ${actionText} ở các trạng thái: Chờ xác nhận, Đã xác nhận, Đang xử lý, Đã thanh toán, Đang đóng gói, Đang chuẩn bị, Đang lên lịch.`,
        code: 'INVALID_CANCELLATION',
        vietnameseMessage: `Không thể ${actionText} ở trạng thái hiện tại`
      };
    }
    return {
      valid: true,
      message: `Đơn hàng sẽ được chuyển sang trạng thái "${getOrderStatusInfo(newStatus)?.label}".`
    };
  }

  // Không cho phép chuyển từ final statuses
  if (FINAL_STATUSES.includes(currentStatus)) {
    return {
      valid: false,
      message: `Đơn hàng đã ở trạng thái "${getOrderStatusInfo(currentStatus)?.label}" và không thể thay đổi. Đây là trạng thái cuối cùng của đơn hàng.`,
      code: 'FINAL_STATUS_LOCKED',
      vietnameseMessage: 'Không thể thay đổi trạng thái đơn hàng đã hoàn tất'
    };
  }

  // === FLOW VALIDATION ===
  
  // Kiểm tra trạng thái có tồn tại trong flow
  if (currentIndex === -1) {
    return {
      valid: false,
      message: `Trạng thái hiện tại "${getOrderStatusInfo(currentStatus)?.label}" không phù hợp với quy trình của loại sản phẩm ${getProductTypeLabel(productType)}. Vui lòng kiểm tra lại cấu hình đơn hàng.`,
      code: 'INVALID_CURRENT_STATUS',
      vietnameseMessage: 'Trạng thái hiện tại không hợp lệ'
    };
  }

  if (newIndex === -1) {
    return {
      valid: false,
      message: `Trạng thái "${getOrderStatusInfo(newStatus)?.label}" không áp dụng được cho loại sản phẩm ${getProductTypeLabel(productType)}. Vui lòng chọn trạng thái phù hợp khác.`,
      code: 'INVALID_NEW_STATUS',
      vietnameseMessage: 'Trạng thái không phù hợp với loại sản phẩm'
    };
  }

  // Không cho phép chuyển ngược
  if (newIndex < currentIndex) {
    return {
      valid: false,
      message: `Không thể quay lại trạng thái trước đó. Đơn hàng hiện tại đang ở "${getOrderStatusInfo(currentStatus)?.label}" và không thể chuyển về "${getOrderStatusInfo(newStatus)?.label}".`,
      code: 'BACKWARD_TRANSITION',
      vietnameseMessage: 'Không thể quay lại trạng thái trước đó'
    };
  }

  // Kiểm tra skip steps
  if (!allowSkipSteps && newIndex - currentIndex > 1) {
    return {
      valid: false,
      message: `Để đảm bảo quy trình chính xác, vui lòng cập nhật đơn hàng từng bước một. Không thể nhảy trực tiếp từ "${getOrderStatusInfo(currentStatus)?.label}" đến "${getOrderStatusInfo(newStatus)?.label}".`,
      code: 'SKIP_STEPS_NOT_ALLOWED',
      vietnameseMessage: 'Vui lòng cập nhật từng bước một'
    };
  }

  if (allowSkipSteps && newIndex - currentIndex > maxSkipSteps) {
    return {
      valid: false,
      message: `Chỉ có thể nhảy tối đa ${maxSkipSteps} bước trong quy trình. Việc chuyển từ "${getOrderStatusInfo(currentStatus)?.label}" đến "${getOrderStatusInfo(newStatus)?.label}" vượt quá giới hạn cho phép.`,
      code: 'MAX_SKIP_STEPS_EXCEEDED',
      vietnameseMessage: `Chỉ có thể nhảy tối đa ${maxSkipSteps} bước`
    };
  }

  return {
    valid: true,
    message: `Đơn hàng sẽ được cập nhật từ "${getOrderStatusInfo(currentStatus)?.label}" sang "${getOrderStatusInfo(newStatus)?.label}".`
  };
}

/**
 * Lấy danh sách trạng thái tiếp theo có thể chuyển
 * @param {string} currentStatus - Trạng thái hiện tại
 * @param {string} productType - Loại sản phẩm
 * @param {Object} options - Tùy chọn
 * @returns {Array} - Danh sách trạng thái có thể chuyển
 */
export function getNextValidStatuses(currentStatus, productType = 'simple', options = {}) {
  const { allowSkipSteps = true, maxSkipSteps = 2, includeCancellation = true } = options;
  
  const flow = getBusinessFlow(productType);
  const currentIndex = flow.indexOf(currentStatus);
  
  if (currentIndex === -1) return [];
  
  // Nếu đã ở final status, không thể chuyển
  if (FINAL_STATUSES.includes(currentStatus)) return [];
  
  const nextStatuses = [];
  
  // Thêm các trạng thái tiếp theo trong flow
  const maxIndex = allowSkipSteps 
    ? Math.min(currentIndex + maxSkipSteps + 1, flow.length)
    : currentIndex + 2;
    
  for (let i = currentIndex + 1; i < maxIndex; i++) {
    nextStatuses.push(flow[i]);
  }
  
  // Thêm cancelled/refunded nếu được phép
  if (includeCancellation && CANCELLABLE_STATUSES.includes(currentStatus)) {
    if (!nextStatuses.includes('cancelled')) nextStatuses.push('cancelled');
    if (!nextStatuses.includes('refunded')) nextStatuses.push('refunded');
  }
  
  return nextStatuses.map(status => {
    const statusInfo = getOrderStatusInfo(status);
    return {
      value: status,
      label: statusInfo?.label || status,
      color: statusInfo?.color || 'default'
    };
  });
}

/**
 * Kiểm tra trạng thái có cần inventory update không
 * @param {string} status - Trạng thái
 * @returns {string|null} - Loại inventory action hoặc null
 */
export function getInventoryImpact(status) {
  return INVENTORY_IMPACT_STATUSES[status] || null;
}

/**
 * Kiểm tra trạng thái có phải final không
 * @param {string} status - Trạng thái
 * @returns {boolean} - True nếu là final status
 */
export function isFinalStatus(status) {
  return FINAL_STATUSES.includes(status);
}

/**
 * Kiểm tra có thể hủy/hoàn tiền không
 * @param {string} status - Trạng thái hiện tại
 * @returns {boolean} - True nếu có thể hủy/hoàn tiền
 */
export function isCancellable(status) {
  return CANCELLABLE_STATUSES.includes(status);
}

/**
 * Lấy label tiếng Việt của product type
 * @param {string} productType - Loại sản phẩm
 * @returns {string} - Label tiếng Việt
 */
export function getProductTypeLabel(productType) {
  return PRODUCT_TYPE_LABELS[productType] || productType;
}
