'use client';

import { useState, useCallback } from 'react';
import useSWR from 'swr';

import { toast } from 'src/components/snackbar';

import {
  getWorkflows,
  getWorkflowDetail,
  createWorkflow,
  updateWorkflow,
  deleteWorkflow,
  createWorkflowFromTemplate,
  createWorkflowStage,
  updateWorkflowStage,
  deleteWorkflowStage,
  DEFAULT_WORKFLOW_TEMPLATES
} from './custom-workflow-service';

/**
 * =====================================================
 * USE CUSTOM WORKFLOW HOOK
 * =====================================================
 * 
 * React hook để quản lý custom workflows:
 * - Fetch workflows với SWR caching
 * - CRUD operations với loading states
 * - Error handling và notifications
 * - Optimistic updates
 */

/**
 * Hook chính để quản lý workflows
 * @param {Object} options - T<PERSON>y chọn
 * @returns {Object} - Workflow data và functions
 */
export function useCustomWorkflow(options = {}) {
  const { productType, isActive = true } = options;
  
  // States
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // SWR key cho workflows
  const swrKey = ['workflows', productType, isActive];
  
  // Fetch workflows với SWR
  const {
    data: workflowsData,
    error: swrError,
    mutate: mutateWorkflows,
    isLoading: swrLoading
  } = useSWR(
    swrKey,
    () => getWorkflows({ productType, isActive }),
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      dedupingInterval: 30000 // 30 seconds
    }
  );

  const workflows = workflowsData?.success ? workflowsData.data : [];

  // Tạo workflow mới
  const handleCreateWorkflow = useCallback(async (workflowData) => {
    setLoading(true);
    setError(null);

    try {
      const result = await createWorkflow(workflowData);
      
      if (result.success) {
        toast.success(result.message || 'Đã tạo workflow thành công');
        
        // Optimistic update
        mutateWorkflows();
        
        return { success: true, data: result.data };
      } else {
        const errorMessage = result.error || 'Không thể tạo workflow';
        setError(errorMessage);
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (err) {
      const errorMessage = err.message || 'Lỗi khi tạo workflow';
      setError(errorMessage);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [mutateWorkflows]);

  // Tạo workflow từ template
  const handleCreateFromTemplate = useCallback(async (templateType, customData = {}) => {
    setLoading(true);
    setError(null);

    try {
      const result = await createWorkflowFromTemplate(templateType, customData);
      
      if (result.success) {
        toast.success(result.message || 'Đã tạo workflow từ template thành công');
        mutateWorkflows();
        return { success: true, data: result.data };
      } else {
        const errorMessage = result.error || 'Không thể tạo workflow từ template';
        setError(errorMessage);
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (err) {
      const errorMessage = err.message || 'Lỗi khi tạo workflow từ template';
      setError(errorMessage);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [mutateWorkflows]);

  // Cập nhật workflow
  const handleUpdateWorkflow = useCallback(async (workflowId, updateData) => {
    setLoading(true);
    setError(null);

    try {
      const result = await updateWorkflow(workflowId, updateData);
      
      if (result.success) {
        toast.success(result.message || 'Đã cập nhật workflow thành công');
        mutateWorkflows();
        return { success: true, data: result.data };
      } else {
        const errorMessage = result.error || 'Không thể cập nhật workflow';
        setError(errorMessage);
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (err) {
      const errorMessage = err.message || 'Lỗi khi cập nhật workflow';
      setError(errorMessage);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [mutateWorkflows]);

  // Xóa workflow
  const handleDeleteWorkflow = useCallback(async (workflowId) => {
    setLoading(true);
    setError(null);

    try {
      const result = await deleteWorkflow(workflowId);
      
      if (result.success) {
        toast.success(result.message || 'Đã xóa workflow thành công');
        mutateWorkflows();
        return { success: true };
      } else {
        const errorMessage = result.error || 'Không thể xóa workflow';
        setError(errorMessage);
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (err) {
      const errorMessage = err.message || 'Lỗi khi xóa workflow';
      setError(errorMessage);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [mutateWorkflows]);

  // Toggle active status
  const handleToggleActive = useCallback(async (workflowId, isActive) => {
    return handleUpdateWorkflow(workflowId, { is_active: isActive });
  }, [handleUpdateWorkflow]);

  // Set as default workflow
  const handleSetDefault = useCallback(async (workflowId, productType) => {
    setLoading(true);
    setError(null);

    try {
      // Đầu tiên, bỏ default của tất cả workflows cùng product_type
      const currentWorkflows = workflows.filter(w => w.product_type === productType);
      
      for (const workflow of currentWorkflows) {
        if (workflow.is_default && workflow.id !== workflowId) {
          await updateWorkflow(workflow.id, { is_default: false });
        }
      }

      // Sau đó set workflow hiện tại làm default
      const result = await handleUpdateWorkflow(workflowId, { is_default: true });
      
      if (result.success) {
        toast.success('Đã đặt làm workflow mặc định');
      }
      
      return result;
    } catch (err) {
      const errorMessage = err.message || 'Lỗi khi đặt workflow mặc định';
      setError(errorMessage);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [workflows, handleUpdateWorkflow]);

  return {
    // Data
    workflows,
    templates: DEFAULT_WORKFLOW_TEMPLATES,
    
    // States
    loading: loading || swrLoading,
    error: error || swrError,
    
    // Actions
    createWorkflow: handleCreateWorkflow,
    createFromTemplate: handleCreateFromTemplate,
    updateWorkflow: handleUpdateWorkflow,
    deleteWorkflow: handleDeleteWorkflow,
    toggleActive: handleToggleActive,
    setDefault: handleSetDefault,
    
    // Utils
    refresh: mutateWorkflows,
    
    // Computed
    activeWorkflows: workflows.filter(w => w.is_active),
    defaultWorkflows: workflows.filter(w => w.is_default),
    workflowsByType: workflows.reduce((acc, workflow) => {
      if (!acc[workflow.product_type]) {
        acc[workflow.product_type] = [];
      }
      acc[workflow.product_type].push(workflow);
      return acc;
    }, {})
  };
}

/**
 * Hook để quản lý chi tiết workflow
 * @param {string} workflowId - ID workflow
 * @returns {Object} - Workflow detail data và functions
 */
export function useWorkflowDetail(workflowId) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // SWR key cho workflow detail
  const swrKey = workflowId ? ['workflow-detail', workflowId] : null;
  
  // Fetch workflow detail với SWR
  const {
    data: workflowData,
    error: swrError,
    mutate: mutateWorkflow,
    isLoading: swrLoading
  } = useSWR(
    swrKey,
    () => getWorkflowDetail(workflowId),
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true
    }
  );

  const workflow = workflowData?.success ? workflowData.data[0] : null;

  // Stage management functions
  const handleCreateStage = useCallback(async (stageData) => {
    if (!workflowId) return { success: false, error: 'Thiếu ID workflow' };

    setLoading(true);
    setError(null);

    try {
      const result = await createWorkflowStage(workflowId, stageData);
      
      if (result.success) {
        toast.success(result.message || 'Đã tạo stage thành công');
        mutateWorkflow();
        return { success: true, data: result.data };
      } else {
        const errorMessage = result.error || 'Không thể tạo stage';
        setError(errorMessage);
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (err) {
      const errorMessage = err.message || 'Lỗi khi tạo stage';
      setError(errorMessage);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [workflowId, mutateWorkflow]);

  const handleUpdateStage = useCallback(async (stageId, updateData) => {
    setLoading(true);
    setError(null);

    try {
      const result = await updateWorkflowStage(stageId, updateData);
      
      if (result.success) {
        toast.success(result.message || 'Đã cập nhật stage thành công');
        mutateWorkflow();
        return { success: true, data: result.data };
      } else {
        const errorMessage = result.error || 'Không thể cập nhật stage';
        setError(errorMessage);
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (err) {
      const errorMessage = err.message || 'Lỗi khi cập nhật stage';
      setError(errorMessage);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [mutateWorkflow]);

  const handleDeleteStage = useCallback(async (stageId) => {
    setLoading(true);
    setError(null);

    try {
      const result = await deleteWorkflowStage(stageId);
      
      if (result.success) {
        toast.success(result.message || 'Đã xóa stage thành công');
        mutateWorkflow();
        return { success: true };
      } else {
        const errorMessage = result.error || 'Không thể xóa stage';
        setError(errorMessage);
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (err) {
      const errorMessage = err.message || 'Lỗi khi xóa stage';
      setError(errorMessage);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [mutateWorkflow]);

  return {
    // Data
    workflow,
    stages: workflow?.stages || [],
    
    // States
    loading: loading || swrLoading,
    error: error || swrError,
    
    // Actions
    createStage: handleCreateStage,
    updateStage: handleUpdateStage,
    deleteStage: handleDeleteStage,
    
    // Utils
    refresh: mutateWorkflow,
    
    // Computed
    sortedStages: (workflow?.stages || []).sort((a, b) => a.sort_order - b.sort_order),
    startStage: (workflow?.stages || []).find(s => s.is_start_stage),
    endStages: (workflow?.stages || []).filter(s => s.is_end_stage)
  };
}
