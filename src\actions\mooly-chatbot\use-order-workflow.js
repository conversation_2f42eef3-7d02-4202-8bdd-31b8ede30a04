'use client';

import { useState, useCallback } from 'react';
import useSWR from 'swr';

import { toast } from 'src/components/snackbar';

import {
  initializeOrderWorkflow,
  transitionOrderWorkflow,
  getOrderWorkflowInstance,
  getNextWorkflowStatuses
} from './workflow-order-integration';

/**
 * =====================================================
 * USE ORDER WORKFLOW HOOK
 * =====================================================
 * 
 * React hook để quản lý workflow của đơn hàng:
 * - Fetch workflow instance với SWR caching
 * - Transition management với loading states
 * - Error handling và notifications
 * - Optimistic updates
 */

/**
 * Hook chính để quản lý workflow của đơn hàng
 * @param {string} orderId - ID đơn hàng
 * @returns {Object} - Workflow data và functions
 */
export function useOrderWorkflow(orderId) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // SWR key cho workflow instance
  const swrKey = orderId ? ['order-workflow', orderId] : null;
  
  // Fetch workflow instance với SWR
  const {
    data: instanceData,
    error: swrError,
    mutate: mutateInstance,
    isLoading: swrLoading
  } = useSWR(
    swrKey,
    () => getOrderWorkflowInstance(orderId),
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      dedupingInterval: 10000 // 10 seconds
    }
  );

  // SWR key cho next statuses
  const nextStatusesKey = orderId ? ['next-statuses', orderId] : null;
  
  // Fetch next statuses với SWR
  const {
    data: nextStatusesData,
    error: nextStatusesError,
    mutate: mutateNextStatuses,
    isLoading: nextStatusesLoading
  } = useSWR(
    nextStatusesKey,
    () => getNextWorkflowStatuses(orderId),
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      dedupingInterval: 10000 // 10 seconds
    }
  );

  const instance = instanceData?.success ? instanceData.data : null;
  const nextStatuses = nextStatusesData?.success ? nextStatusesData.data : [];

  // Khởi tạo workflow cho đơn hàng
  const handleInitializeWorkflow = useCallback(async (productType, options = {}) => {
    if (!orderId) return { success: false, error: 'Thiếu ID đơn hàng' };

    setLoading(true);
    setError(null);

    try {
      const result = await initializeOrderWorkflow(orderId, productType, options);
      
      if (result.success) {
        toast.success(result.message || 'Đã khởi tạo workflow thành công');
        mutateInstance();
        mutateNextStatuses();
        return { success: true, data: result.data };
      } else {
        const errorMessage = result.error || 'Không thể khởi tạo workflow';
        setError(errorMessage);
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (err) {
      const errorMessage = err.message || 'Lỗi khi khởi tạo workflow';
      setError(errorMessage);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [orderId, mutateInstance, mutateNextStatuses]);

  // Chuyển đổi trạng thái đơn hàng
  const handleTransitionWorkflow = useCallback(async (newStatus, options = {}) => {
    if (!orderId) return { success: false, error: 'Thiếu ID đơn hàng' };

    setLoading(true);
    setError(null);

    try {
      const result = await transitionOrderWorkflow(orderId, newStatus, options);
      
      if (result.success) {
        toast.success(result.message || 'Đã cập nhật trạng thái đơn hàng thành công');
        mutateInstance();
        mutateNextStatuses();
        return { success: true, data: result.data };
      } else {
        const errorMessage = result.error || 'Không thể cập nhật trạng thái đơn hàng';
        setError(errorMessage);
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (err) {
      const errorMessage = err.message || 'Lỗi khi cập nhật trạng thái đơn hàng';
      setError(errorMessage);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [orderId, mutateInstance, mutateNextStatuses]);

  return {
    // Data
    instance,
    workflow: instance?.workflow,
    currentStage: instance?.current_stage,
    history: instance?.history,
    nextStatuses,
    
    // States
    loading: loading || swrLoading || nextStatusesLoading,
    error: error || swrError || nextStatusesError,
    
    // Actions
    initializeWorkflow: handleInitializeWorkflow,
    transitionWorkflow: handleTransitionWorkflow,
    
    // Utils
    refresh: () => {
      mutateInstance();
      mutateNextStatuses();
    },
    
    // Computed
    hasWorkflow: !!instance,
    isCompleted: !!instance?.completed_at,
    sortedHistory: instance?.history?.sort((a, b) => 
      new Date(b.created_at) - new Date(a.created_at)
    ) || []
  };
}

/**
 * Hook để quản lý workflow cho nhiều đơn hàng
 * @param {Array} orderIds - Danh sách ID đơn hàng
 * @returns {Object} - Workflow data và functions
 */
export function useBulkOrderWorkflow(orderIds = []) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [results, setResults] = useState({});

  // Chuyển đổi trạng thái nhiều đơn hàng
  const handleBulkTransition = useCallback(async (newStatus, options = {}) => {
    if (!orderIds.length) {
      return { success: false, error: 'Không có đơn hàng nào được chọn' };
    }

    setLoading(true);
    setError(null);
    setResults({});

    const bulkResults = {};
    let successCount = 0;
    let errorCount = 0;

    try {
      for (const orderId of orderIds) {
        try {
          const result = await transitionOrderWorkflow(orderId, newStatus, options);
          
          bulkResults[orderId] = result;
          
          if (result.success) {
            successCount++;
          } else {
            errorCount++;
          }
        } catch (err) {
          bulkResults[orderId] = {
            success: false,
            error: err.message || 'Lỗi không xác định'
          };
          errorCount++;
        }
      }

      setResults(bulkResults);

      if (errorCount === 0) {
        toast.success(`Đã cập nhật ${successCount} đơn hàng thành công`);
        return { success: true, data: bulkResults };
      } else if (successCount === 0) {
        const errorMessage = `Không thể cập nhật ${errorCount} đơn hàng`;
        setError(errorMessage);
        toast.error(errorMessage);
        return { success: false, error: errorMessage, data: bulkResults };
      } else {
        const warningMessage = `Đã cập nhật ${successCount} đơn hàng thành công, ${errorCount} đơn hàng thất bại`;
        toast.warning(warningMessage);
        return { 
          success: true, 
          warning: true, 
          message: warningMessage,
          data: bulkResults 
        };
      }
    } catch (err) {
      const errorMessage = err.message || 'Lỗi khi cập nhật hàng loạt';
      setError(errorMessage);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [orderIds]);

  return {
    // States
    loading,
    error,
    results,
    
    // Actions
    bulkTransition: handleBulkTransition,
    
    // Computed
    successCount: Object.values(results).filter(r => r.success).length,
    errorCount: Object.values(results).filter(r => !r.success).length,
    hasErrors: Object.values(results).some(r => !r.success)
  };
}
