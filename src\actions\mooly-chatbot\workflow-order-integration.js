'use client';

import { fetchData, createData, updateData, callRPC } from './supabase-utils';
import { validateOrderStatusTransition } from './order-status-business-rules';

/**
 * =====================================================
 * WORKFLOW ORDER INTEGRATION SERVICE
 * =====================================================
 * 
 * Tích hợp custom workflow vào order management:
 * - Khởi tạo workflow instance cho đơn hàng mới
 * - X<PERSON> lý transitions theo custom workflow
 * - Validation theo business rules của workflow
 * - Tracking workflow history
 */

// Table names
const WORKFLOWS_TABLE = 'order_workflows';
const STAGES_TABLE = 'workflow_stages';
const TRANSITIONS_TABLE = 'order_workflow_transitions';
const INSTANCES_TABLE = 'order_workflow_instances';
const HISTORY_TABLE = 'order_workflow_history';

/**
 * Lấy default workflow cho product type
 * @param {string} productType - Loại sản phẩm
 * @returns {Promise<Object>} - Default workflow
 */
export async function getDefaultWorkflow(productType) {
  try {
    const result = await fetchData(WORKFLOWS_TABLE, {
      filters: {
        product_type: productType,
        is_default: true,
        is_active: true
      },
      select: `
        *,
        stages:workflow_stages(
          *,
          transitions_from:order_workflow_transitions!from_stage_id(*)
        )
      `,
      limit: 1
    });

    if (result.success && result.data.length > 0) {
      return { success: true, data: result.data[0] };
    }

    // Fallback: lấy workflow active đầu tiên
    const fallbackResult = await fetchData(WORKFLOWS_TABLE, {
      filters: {
        product_type: productType,
        is_active: true
      },
      select: `
        *,
        stages:workflow_stages(
          *,
          transitions_from:order_workflow_transitions!from_stage_id(*)
        )
      `,
      orderBy: 'created_at',
      ascending: false,
      limit: 1
    });

    if (fallbackResult.success && fallbackResult.data.length > 0) {
      return { success: true, data: fallbackResult.data[0] };
    }

    return {
      success: false,
      error: `Không tìm thấy workflow cho loại sản phẩm ${productType}`
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi lấy default workflow'
    };
  }
}

/**
 * Khởi tạo workflow instance cho đơn hàng mới
 * @param {string} orderId - ID đơn hàng
 * @param {string} productType - Loại sản phẩm
 * @param {Object} options - Tùy chọn
 * @returns {Promise<Object>} - Kết quả khởi tạo
 */
export async function initializeOrderWorkflow(orderId, productType, options = {}) {
  try {
    const { workflowId, startStatus = 'pending' } = options;

    let workflow;
    
    if (workflowId) {
      // Sử dụng workflow được chỉ định
      const workflowResult = await fetchData(WORKFLOWS_TABLE, {
        filters: { id: workflowId },
        select: `
          *,
          stages:workflow_stages(*)
        `
      });
      
      if (!workflowResult.success || workflowResult.data.length === 0) {
        throw new Error('Workflow không tồn tại');
      }
      
      workflow = workflowResult.data[0];
    } else {
      // Lấy default workflow
      const defaultResult = await getDefaultWorkflow(productType);
      if (!defaultResult.success) {
        throw new Error(defaultResult.error);
      }
      workflow = defaultResult.data;
    }

    // Tìm start stage
    const startStage = workflow.stages.find(stage => 
      stage.is_start_stage || stage.status_code === startStatus
    );

    if (!startStage) {
      throw new Error('Không tìm thấy stage bắt đầu trong workflow');
    }

    // Tạo workflow instance
    const instanceResult = await createData(INSTANCES_TABLE, {
      order_id: orderId,
      workflow_id: workflow.id,
      current_stage_id: startStage.id,
      metadata: {
        product_type: productType,
        initialized_at: new Date().toISOString(),
        start_stage: startStage.status_code
      }
    });

    if (!instanceResult.success) {
      throw new Error(instanceResult.error?.message || 'Không thể tạo workflow instance');
    }

    // Tạo history record đầu tiên
    await createData(HISTORY_TABLE, {
      order_id: orderId,
      workflow_instance_id: instanceResult.data[0].id,
      to_stage_id: startStage.id,
      trigger_type: 'automatic',
      metadata: {
        action: 'initialize',
        workflow_name: workflow.name,
        stage_name: startStage.name
      }
    });

    return {
      success: true,
      data: {
        instance: instanceResult.data[0],
        workflow,
        currentStage: startStage
      },
      message: `Đã khởi tạo quy trình "${workflow.name}" cho đơn hàng`
    };

  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi khởi tạo workflow'
    };
  }
}

/**
 * Chuyển đổi trạng thái đơn hàng theo workflow
 * @param {string} orderId - ID đơn hàng
 * @param {string} newStatus - Trạng thái mới
 * @param {Object} options - Tùy chọn
 * @returns {Promise<Object>} - Kết quả chuyển đổi
 */
export async function transitionOrderWorkflow(orderId, newStatus, options = {}) {
  try {
    const { 
      userId, 
      comment, 
      triggerType = 'manual',
      autoInventoryUpdate = true,
      skipValidation = false 
    } = options;

    // 1. Lấy workflow instance hiện tại
    const instanceResult = await fetchData(INSTANCES_TABLE, {
      filters: { order_id: orderId },
      select: `
        *,
        workflow:order_workflows(*),
        current_stage:workflow_stages(*)
      `
    });

    if (!instanceResult.success || instanceResult.data.length === 0) {
      return {
        success: false,
        error: 'Không tìm thấy workflow instance cho đơn hàng này'
      };
    }

    const instance = instanceResult.data[0];
    const currentStage = instance.current_stage;
    const workflow = instance.workflow;

    // 2. Tìm target stage
    const targetStageResult = await fetchData(STAGES_TABLE, {
      filters: {
        workflow_id: workflow.id,
        status_code: newStatus
      }
    });

    if (!targetStageResult.success || targetStageResult.data.length === 0) {
      return {
        success: false,
        error: `Trạng thái "${newStatus}" không tồn tại trong quy trình "${workflow.name}"`
      };
    }

    const targetStage = targetStageResult.data[0];

    // 3. Validate transition nếu cần
    if (!skipValidation) {
      const validationResult = validateOrderStatusTransition(
        currentStage.status_code,
        newStatus,
        workflow.product_type,
        { allowSkipSteps: true, maxSkipSteps: 2 }
      );

      if (!validationResult.valid) {
        return {
          success: false,
          error: validationResult.message,
          code: validationResult.code
        };
      }
    }

    // 4. Kiểm tra transition có tồn tại không (optional)
    const transitionResult = await fetchData(TRANSITIONS_TABLE, {
      filters: {
        workflow_id: workflow.id,
        from_stage_id: currentStage.id,
        to_stage_id: targetStage.id
      }
    });

    const transition = transitionResult.success && transitionResult.data.length > 0 
      ? transitionResult.data[0] 
      : null;

    // 5. Thực hiện transition
    const startTime = Date.now();

    // Cập nhật order status thông qua database function
    const updateResult = await callRPC('update_order_status_with_history', {
      p_order_id: orderId,
      p_new_status: newStatus,
      p_previous_status: currentStage.status_code,
      p_comment: comment || `Chuyển từ "${currentStage.name}" sang "${targetStage.name}"`,
      p_user_id: userId,
      p_auto_inventory_update: autoInventoryUpdate
    });

    if (!updateResult.success) {
      throw new Error(updateResult.error || 'Không thể cập nhật trạng thái đơn hàng');
    }

    // 6. Cập nhật workflow instance
    await updateData(INSTANCES_TABLE, {
      current_stage_id: targetStage.id,
      completed_at: targetStage.is_end_stage ? new Date().toISOString() : null,
      metadata: {
        ...instance.metadata,
        last_transition: new Date().toISOString(),
        transition_count: (instance.metadata?.transition_count || 0) + 1
      }
    }, { id: instance.id });

    // 7. Tạo workflow history
    const executionTime = Date.now() - startTime;
    
    await createData(HISTORY_TABLE, {
      order_id: orderId,
      workflow_instance_id: instance.id,
      from_stage_id: currentStage.id,
      to_stage_id: targetStage.id,
      transition_id: transition?.id,
      triggered_by: userId,
      trigger_type: triggerType,
      success: true,
      execution_time: executionTime,
      metadata: {
        comment,
        from_stage_name: currentStage.name,
        to_stage_name: targetStage.name,
        transition_name: transition?.name,
        auto_inventory_update: autoInventoryUpdate,
        order_update_result: updateResult.data
      }
    });

    return {
      success: true,
      data: {
        instance,
        fromStage: currentStage,
        toStage: targetStage,
        transition,
        orderUpdateResult: updateResult.data
      },
      message: `Đã chuyển đơn hàng từ "${currentStage.name}" sang "${targetStage.name}"`
    };

  } catch (error) {
    // Log lỗi vào workflow history
    try {
      const instanceResult = await fetchData(INSTANCES_TABLE, {
        filters: { order_id: orderId }
      });

      if (instanceResult.success && instanceResult.data.length > 0) {
        await createData(HISTORY_TABLE, {
          order_id: orderId,
          workflow_instance_id: instanceResult.data[0].id,
          to_stage_id: instanceResult.data[0].current_stage_id,
          triggered_by: options.userId,
          trigger_type: options.triggerType || 'manual',
          success: false,
          error_message: error.message,
          metadata: {
            attempted_status: newStatus,
            error_details: error.stack
          }
        });
      }
    } catch (logError) {
      console.error('Failed to log workflow error:', logError);
    }

    return {
      success: false,
      error: error.message || 'Lỗi khi chuyển đổi workflow'
    };
  }
}

/**
 * Lấy workflow instance của đơn hàng
 * @param {string} orderId - ID đơn hàng
 * @returns {Promise<Object>} - Workflow instance
 */
export async function getOrderWorkflowInstance(orderId) {
  try {
    const result = await fetchData(INSTANCES_TABLE, {
      filters: { order_id: orderId },
      select: `
        *,
        workflow:order_workflows(*),
        current_stage:workflow_stages(*),
        history:order_workflow_history(
          *,
          from_stage:workflow_stages!from_stage_id(*),
          to_stage:workflow_stages!to_stage_id(*),
          transition:order_workflow_transitions(*)
        )
      `
    });

    if (result.success && result.data.length > 0) {
      return { success: true, data: result.data[0] };
    }

    return {
      success: false,
      error: 'Không tìm thấy workflow instance cho đơn hàng này'
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi lấy workflow instance'
    };
  }
}

/**
 * Lấy các trạng thái tiếp theo có thể chuyển theo workflow
 * @param {string} orderId - ID đơn hàng
 * @returns {Promise<Object>} - Danh sách trạng thái tiếp theo
 */
export async function getNextWorkflowStatuses(orderId) {
  try {
    const instanceResult = await getOrderWorkflowInstance(orderId);
    if (!instanceResult.success) {
      return instanceResult;
    }

    const instance = instanceResult.data;
    const currentStage = instance.current_stage;
    const workflow = instance.workflow;

    // Lấy các transitions từ stage hiện tại
    const transitionsResult = await fetchData(TRANSITIONS_TABLE, {
      filters: { from_stage_id: currentStage.id },
      select: `
        *,
        to_stage:workflow_stages!to_stage_id(*)
      `
    });

    let nextStatuses = [];

    if (transitionsResult.success) {
      nextStatuses = transitionsResult.data.map(transition => ({
        value: transition.to_stage.status_code,
        label: transition.to_stage.name,
        color: transition.to_stage.color,
        stage: transition.to_stage,
        transition: transition,
        requires_confirmation: transition.requires_confirmation,
        is_automatic: transition.is_automatic
      }));
    }

    // Luôn cho phép cancelled/refunded nếu có thể
    const cancellableStatuses = ['pending', 'confirmed', 'processing', 'paid', 'packaging', 'preparing', 'scheduling'];
    
    if (cancellableStatuses.includes(currentStage.status_code)) {
      // Tìm cancelled và refunded stages trong workflow
      const specialStagesResult = await fetchData(STAGES_TABLE, {
        filters: {
          workflow_id: workflow.id,
          status_code: ['cancelled', 'refunded']
        }
      });

      if (specialStagesResult.success) {
        specialStagesResult.data.forEach(stage => {
          if (!nextStatuses.some(s => s.value === stage.status_code)) {
            nextStatuses.push({
              value: stage.status_code,
              label: stage.name,
              color: stage.color,
              stage: stage,
              transition: null,
              requires_confirmation: true,
              is_automatic: false
            });
          }
        });
      }
    }

    return {
      success: true,
      data: nextStatuses,
      currentStage,
      workflow
    };

  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi lấy trạng thái tiếp theo'
    };
  }
}
