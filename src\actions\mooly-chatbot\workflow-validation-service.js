'use client';

import { getOrderStatusInfo } from './order-constants';
import {
  getBusinessFlow,
  validateOrderStatusTransition,
  FINAL_STATUSES,
  CANCELLABLE_STATUSES
} from './order-status-business-rules';

/**
 * =====================================================
 * WORKFLOW VALIDATION SERVICE
 * =====================================================
 * 
 * Comprehensive validation cho workflow configuration:
 * - Business rules compliance
 * - Stage flow validation
 * - Transition rules
 * - Performance optimization
 */

/**
 * Validate workflow configuration
 * @param {Object} workflowData - Workflow data to validate
 * @param {Array} existingWorkflows - Existing workflows for duplicate check
 * @returns {Object} - Validation result
 */
export function validateWorkflowConfiguration(workflowData, existingWorkflows = []) {
  const errors = [];
  const warnings = [];
  const suggestions = [];

  // 1. Basic validation
  const basicValidation = validateBasicWorkflowData(workflowData);
  errors.push(...basicValidation.errors);
  warnings.push(...basicValidation.warnings);

  // 2. Duplicate name check
  const duplicateCheck = validateWorkflowUniqueness(workflowData, existingWorkflows);
  errors.push(...duplicateCheck.errors);

  // 3. Stages validation
  if (workflowData.stages && Array.isArray(workflowData.stages)) {
    const stagesValidation = validateWorkflowStages(workflowData.stages, workflowData.product_type);
    errors.push(...stagesValidation.errors);
    warnings.push(...stagesValidation.warnings);
    suggestions.push(...stagesValidation.suggestions);
  }

  // 4. Business flow compliance
  const businessFlowValidation = validateBusinessFlowCompliance(workflowData);
  warnings.push(...businessFlowValidation.warnings);
  suggestions.push(...businessFlowValidation.suggestions);

  // 5. Performance optimization suggestions
  const performanceCheck = validateWorkflowPerformance(workflowData);
  suggestions.push(...performanceCheck.suggestions);

  return {
    isValid: errors.length === 0,
    errors: errors.filter(Boolean),
    warnings: warnings.filter(Boolean),
    suggestions: suggestions.filter(Boolean),
    score: calculateWorkflowScore(workflowData, errors, warnings)
  };
}

/**
 * Validate basic workflow data
 */
function validateBasicWorkflowData(workflowData) {
  const errors = [];
  const warnings = [];

  // Required fields
  if (!workflowData.name?.trim()) {
    errors.push('Tên workflow không được để trống');
  } else if (workflowData.name.length < 3) {
    errors.push('Tên workflow phải có ít nhất 3 ký tự');
  } else if (workflowData.name.length > 100) {
    errors.push('Tên workflow không được quá 100 ký tự');
  }

  if (!workflowData.product_type) {
    errors.push('Loại sản phẩm không được để trống');
  }

  // Optional but recommended fields
  if (!workflowData.description?.trim()) {
    warnings.push('Nên thêm mô tả để team hiểu rõ mục đích workflow');
  }

  return { errors, warnings };
}

/**
 * Validate workflow uniqueness
 */
function validateWorkflowUniqueness(workflowData, existingWorkflows) {
  const errors = [];

  const duplicateName = existingWorkflows.find(w => 
    w.name.toLowerCase().trim() === workflowData.name?.toLowerCase().trim() &&
    w.product_type === workflowData.product_type &&
    w.id !== workflowData.id // Exclude self when updating
  );

  if (duplicateName) {
    errors.push(`Tên workflow "${workflowData.name}" đã tồn tại cho loại sản phẩm này`);
  }

  return { errors };
}

/**
 * Validate workflow stages
 */
function validateWorkflowStages(stages, productType) {
  const errors = [];
  const warnings = [];
  const suggestions = [];

  if (stages.length === 0) {
    errors.push('Workflow phải có ít nhất một stage');
    return { errors, warnings, suggestions };
  }

  // Check for duplicate status codes
  const statusCodes = stages.map(s => s.status_code).filter(Boolean);
  const duplicates = statusCodes.filter((code, index) => statusCodes.indexOf(code) !== index);
  if (duplicates.length > 0) {
    errors.push(`Trạng thái bị trùng lặp: ${[...new Set(duplicates)].join(', ')}`);
  }

  // Validate start/end stages
  const startStages = stages.filter(s => s.is_start_stage);
  const endStages = stages.filter(s => s.is_end_stage);

  if (startStages.length === 0) {
    errors.push('Workflow phải có ít nhất một stage bắt đầu');
  } else if (startStages.length > 1) {
    errors.push('Workflow chỉ có thể có một stage bắt đầu');
  }

  if (endStages.length === 0) {
    warnings.push('Nên có ít nhất một stage kết thúc để hoàn tất quy trình');
  }

  // Validate individual stages
  stages.forEach((stage, index) => {
    const stageValidation = validateSingleStage(stage, index, productType);
    errors.push(...stageValidation.errors);
    warnings.push(...stageValidation.warnings);
    suggestions.push(...stageValidation.suggestions);
  });

  // Validate stage flow
  const flowValidation = validateStageFlow(stages, productType);
  warnings.push(...flowValidation.warnings);
  suggestions.push(...flowValidation.suggestions);

  return { errors, warnings, suggestions };
}

/**
 * Validate single stage
 */
function validateSingleStage(stage, index, productType) {
  const errors = [];
  const warnings = [];
  const suggestions = [];

  const stageLabel = `Stage ${index + 1}`;

  // Required fields
  if (!stage.name?.trim()) {
    errors.push(`${stageLabel}: Tên stage không được để trống`);
  }

  if (!stage.status_code) {
    errors.push(`${stageLabel}: Status code không được để trống`);
  } else {
    // Validate status code exists
    const statusInfo = getOrderStatusInfo(stage.status_code);
    if (!statusInfo) {
      errors.push(`${stageLabel}: Status code "${stage.status_code}" không hợp lệ`);
    }
  }

  // Business logic validation
  if (stage.requires_payment && !['paid', 'confirmed'].includes(stage.status_code)) {
    warnings.push(`${stageLabel}: Stage yêu cầu thanh toán nhưng status không phù hợp`);
  }

  if (stage.requires_inventory && !['packaging', 'shipping', 'processing'].includes(stage.status_code)) {
    warnings.push(`${stageLabel}: Stage yêu cầu kho hàng nhưng status không phù hợp`);
  }

  if (stage.auto_transition && stage.requires_confirmation) {
    warnings.push(`${stageLabel}: Stage tự động nhưng vẫn yêu cầu xác nhận`);
  }

  // Performance suggestions
  if (stage.auto_transition_delay > 3600) { // > 1 hour
    suggestions.push(`${stageLabel}: Thời gian delay quá lâu có thể ảnh hưởng trải nghiệm`);
  }

  return { errors, warnings, suggestions };
}

/**
 * Validate stage flow logic
 */
function validateStageFlow(stages, productType) {
  const warnings = [];
  const suggestions = [];

  const sortedStages = [...stages].sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));
  
  // Check for logical flow
  for (let i = 0; i < sortedStages.length - 1; i++) {
    const currentStage = sortedStages[i];
    const nextStage = sortedStages[i + 1];

    // Validate transition possibility
    const transitionValidation = validateOrderStatusTransition(
      currentStage.status_code,
      nextStage.status_code,
      productType,
      { allowSkipSteps: true, maxSkipSteps: 2 }
    );

    if (!transitionValidation.valid) {
      warnings.push(`Chuyển từ "${currentStage.name}" sang "${nextStage.name}" có thể không hợp lý`);
    }
  }

  // Check for final stages in middle
  const finalStageInMiddle = sortedStages.find((stage, index) => 
    FINAL_STATUSES.includes(stage.status_code) && index < sortedStages.length - 1
  );

  if (finalStageInMiddle) {
    warnings.push(`Stage "${finalStageInMiddle.name}" là trạng thái cuối nhưng không ở cuối quy trình`);
  }

  return { warnings, suggestions };
}

/**
 * Validate business flow compliance
 */
function validateBusinessFlowCompliance(workflowData) {
  const warnings = [];
  const suggestions = [];

  if (!workflowData.stages || !workflowData.product_type) {
    return { warnings, suggestions };
  }

  const businessFlow = getBusinessFlow(workflowData.product_type);
  const workflowStatuses = workflowData.stages.map(s => s.status_code);

  // Check missing important statuses
  const missingImportantStatuses = businessFlow.filter(status => 
    !workflowStatuses.includes(status) && 
    ['pending', 'confirmed', 'completed'].includes(status)
  );

  if (missingImportantStatuses.length > 0) {
    suggestions.push(`Xem xét thêm các trạng thái quan trọng: ${missingImportantStatuses.join(', ')}`);
  }

  // Check for non-standard statuses
  const nonStandardStatuses = workflowStatuses.filter(status => 
    !businessFlow.includes(status)
  );

  if (nonStandardStatuses.length > 0) {
    warnings.push(`Các trạng thái không chuẩn: ${nonStandardStatuses.join(', ')}`);
  }

  return { warnings, suggestions };
}

/**
 * Validate workflow performance
 */
function validateWorkflowPerformance(workflowData) {
  const suggestions = [];

  if (!workflowData.stages) return { suggestions };

  const stageCount = workflowData.stages.length;
  const autoStages = workflowData.stages.filter(s => s.auto_transition).length;
  const manualStages = stageCount - autoStages;

  // Too many stages
  if (stageCount > 10) {
    suggestions.push('Quy trình có quá nhiều bước, xem xét đơn giản hóa');
  }

  // Too many manual stages
  if (manualStages > 6) {
    suggestions.push('Nhiều bước thủ công, xem xét tự động hóa một số bước');
  }

  // No automation
  if (autoStages === 0 && stageCount > 3) {
    suggestions.push('Xem xét tự động hóa một số bước để tăng hiệu quả');
  }

  return { suggestions };
}

/**
 * Calculate workflow quality score
 */
function calculateWorkflowScore(workflowData, errors, warnings) {
  let score = 100;

  // Deduct for errors
  score -= errors.length * 20;

  // Deduct for warnings
  score -= warnings.length * 5;

  // Bonus for good practices
  if (workflowData.description?.length > 20) score += 5;
  if (workflowData.stages?.some(s => s.auto_transition)) score += 10;
  if (workflowData.stages?.length >= 3 && workflowData.stages?.length <= 7) score += 5;

  return Math.max(0, Math.min(100, score));
}
