'use client';

import { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import Timeline from '@mui/lab/Timeline';
import Avatar from '@mui/material/Avatar';
import TimelineDot from '@mui/lab/TimelineDot';
import TimelineItem from '@mui/lab/TimelineItem';
import Typography from '@mui/material/Typography';
import TimelineContent from '@mui/lab/TimelineContent';
import TimelineSeparator from '@mui/lab/TimelineSeparator';
import TimelineConnector from '@mui/lab/TimelineConnector';
import CircularProgress from '@mui/material/CircularProgress';

import { fDate, fTime } from 'src/utils/format-time';

import { fetchData } from 'src/actions/mooly-chatbot/supabase-utils';
import { useEnhancedOrderService } from 'src/actions/mooly-chatbot/enhanced-order-service';

import { Iconify } from 'src/components/iconify';
import { EmptyContent } from 'src/components/empty-content';

// ==========================================
// 📈 ENHANCED ORDER HISTORY COMPONENT
// ==========================================

export function EnhancedOrderHistory({ orderId, showInventoryImpact = true }) {
  const [history, setHistory] = useState([]);
  const [inventoryTransactions, setInventoryTransactions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const { getStatusColor, getStatusLabel } = useEnhancedOrderService();

  useEffect(() => {
    if (orderId) {
      loadOrderHistory();
      if (showInventoryImpact) {
        loadInventoryTransactions();
      }
    }
  }, [orderId, showInventoryImpact]);

  const loadOrderHistory = async () => {
    try {
      setIsLoading(true);
      const result = await fetchData('order_history', {
        filters: { order_id: orderId },
        orderBy: 'created_at',
        ascending: false,
        select: `
          *,
          users:user_id (
            id,
            full_name,
            avatar_url
          )
        `
      });

      if (result.success) {
        setHistory(result.data || []);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const loadInventoryTransactions = async () => {
    try {
      const result = await fetchData('inventory_transactions', {
        filters: { 
          reference_id: orderId,
          reference_type: 'order'
        },
        orderBy: 'created_at',
        ascending: false,
        select: `
          *,
          products:product_id (
            id,
            name,
            sku
          ),
          product_variants:variant_id (
            id,
            name,
            sku
          )
        `
      });

      if (result.success) {
        setInventoryTransactions(result.data || []);
      }
    } catch (err) {
      console.error('Error loading inventory transactions:', err);
    }
  };

  const getStatusIcon = (status) => {
    const iconMap = {
      // Quy trình chung
      pending: 'solar:clock-circle-bold',
      confirmed: 'solar:check-circle-bold',
      processing: 'solar:settings-bold',
      paid: 'solar:card-bold',
      completed: 'solar:check-circle-bold',

      // Sản phẩm vật lý
      packaging: 'solar:box-bold',
      shipping: 'solar:delivery-bold',
      delivered: 'solar:home-bold',

      // Sản phẩm số
      preparing: 'solar:settings-bold',
      ready_download: 'solar:download-bold',
      sent: 'solar:send-bold',

      // Dịch vụ
      scheduling: 'solar:calendar-bold',
      in_progress: 'solar:play-bold',
      provided: 'solar:check-circle-bold',

      // Trạng thái đặc biệt
      cancelled: 'solar:close-circle-bold',
      refunded: 'solar:restart-bold'
    };

    return iconMap[status] || 'solar:info-circle-bold';
  };

  const getInventoryIcon = (type) => {
    const iconMap = {
      reserve: 'solar:bookmark-bold',
      release: 'solar:restart-bold',
      confirm_sale: 'solar:check-circle-bold',
      adjustment: 'solar:settings-bold'
    };

    return iconMap[type] || 'solar:box-bold';
  };

  const getInventoryColor = (type) => {
    const colorMap = {
      reserve: 'warning',
      release: 'success',
      confirm_sale: 'info',
      adjustment: 'default'
    };

    return colorMap[type] || 'default';
  };

  const renderHistoryItem = (item, index) => {
    const isLast = index === history.length - 1;
    const user = item.users;

    return (
      <TimelineItem key={item.id}>
        <TimelineSeparator>
          <TimelineDot color={getStatusColor(item.status)}>
            <Iconify icon={getStatusIcon(item.status)} width={16} />
          </TimelineDot>
          {!isLast && <TimelineConnector />}
        </TimelineSeparator>

        <TimelineContent>
          <Card sx={{ p: 2, mb: 2 }}>
            <Stack spacing={1}>
              <Stack direction="row" alignItems="center" justifyContent="space-between">
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Chip
                    label={getStatusLabel(item.status)}
                    color={getStatusColor(item.status)}
                    size="small"
                  />
                  {item.previous_status && (
                    <>
                      <Iconify icon="solar:arrow-right-bold" width={12} />
                      <Typography variant="caption" color="text.secondary">
                        từ {getStatusLabel(item.previous_status)}
                      </Typography>
                    </>
                  )}
                </Stack>
                
                <Typography variant="caption" color="text.secondary">
                  {fDate(item.created_at)} {fTime(item.created_at)}
                </Typography>
              </Stack>

              {item.comment && (
                <Typography variant="body2" color="text.secondary">
                  {item.comment}
                </Typography>
              )}

              {user && (
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Avatar
                    src={user.avatar_url}
                    alt={user.full_name}
                    sx={{ width: 20, height: 20 }}
                  />
                  <Typography variant="caption" color="text.secondary">
                    bởi {user.full_name}
                  </Typography>
                </Stack>
              )}
            </Stack>
          </Card>
        </TimelineContent>
      </TimelineItem>
    );
  };

  const renderInventoryItem = (transaction, index) => {
    const isLast = index === inventoryTransactions.length - 1;
    const product = transaction.products;
    const variant = transaction.product_variants;

    return (
      <TimelineItem key={transaction.id}>
        <TimelineSeparator>
          <TimelineDot color={getInventoryColor(transaction.type)}>
            <Iconify icon={getInventoryIcon(transaction.type)} width={16} />
          </TimelineDot>
          {!isLast && <TimelineConnector />}
        </TimelineSeparator>

        <TimelineContent>
          <Card sx={{ p: 2, mb: 2, bgcolor: 'background.neutral' }}>
            <Stack spacing={1}>
              <Stack direction="row" alignItems="center" justifyContent="space-between">
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Iconify icon="solar:box-bold" width={16} />
                  <Typography variant="subtitle2">
                    Cập nhật kho hàng
                  </Typography>
                </Stack>
                
                <Typography variant="caption" color="text.secondary">
                  {fDate(transaction.created_at)} {fTime(transaction.created_at)}
                </Typography>
              </Stack>

              <Stack direction="row" alignItems="center" spacing={2}>
                <Typography variant="body2">
                  {product?.name || 'Sản phẩm không xác định'}
                  {variant && ` - ${variant.name}`}
                </Typography>
                
                <Chip
                  label={`${transaction.type === 'reserve' ? '-' : '+'}${transaction.quantity}`}
                  color={getInventoryColor(transaction.type)}
                  size="small"
                />
              </Stack>

              {transaction.notes && (
                <Typography variant="body2" color="text.secondary">
                  {transaction.notes}
                </Typography>
              )}

              <Stack direction="row" spacing={2}>
                <Typography variant="caption" color="text.secondary">
                  Trước: {transaction.previous_quantity || 0}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Sau: {transaction.current_quantity || 0}
                </Typography>
              </Stack>
            </Stack>
          </Card>
        </TimelineContent>
      </TimelineItem>
    );
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <EmptyContent
        title="Lỗi khi tải lịch sử"
        description={error}
        imgUrl="/assets/icons/empty/ic_content.svg"
      />
    );
  }

  if (history.length === 0 && inventoryTransactions.length === 0) {
    return (
      <EmptyContent
        title="Chưa có lịch sử"
        description="Đơn hàng này chưa có lịch sử thay đổi trạng thái"
        imgUrl="/assets/icons/empty/ic_content.svg"
      />
    );
  }

  // Merge and sort history and inventory transactions by date
  const allEvents = [
    ...history.map(item => ({ ...item, type: 'status', timestamp: item.created_at })),
    ...(showInventoryImpact ? inventoryTransactions.map(item => ({ 
      ...item, 
      type: 'inventory', 
      timestamp: item.created_at 
    })) : [])
  ].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

  return (
    <Box sx={{ p: 1 }}>
      <Timeline>
        {allEvents.map((event, index) => {
          if (event.type === 'status') {
            return renderHistoryItem(event, index);
          } else {
            return renderInventoryItem(event, index);
          }
        })}
      </Timeline>
    </Box>
  );
}
