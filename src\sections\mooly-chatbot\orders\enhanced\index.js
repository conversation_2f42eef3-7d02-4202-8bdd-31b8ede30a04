// ==========================================
// 🚀 ENHANCED ORDER MANAGEMENT SYSTEM
// ==========================================

export { EnhancedOrderHistory } from '../enhanced-order-history';

export { EnhancedOrderTableRow } from '../enhanced-order-table-row';
export { EnhancedOrderAnalytics } from '../enhanced-order-analytics';
export { EnhancedOrderDashboard } from '../enhanced-order-dashboard';
export { EnhancedBatchOperations } from '../enhanced-batch-operations';
// UI Components
export { EnhancedOrderStatusDialog } from '../enhanced-order-status-dialog';
// Views
export { default as OrderListView } from '../view/order-list-view';

// Core Service
export { 
  STATUS_FLOWS,
  INVENTORY_IMPACT,
  getOrderAnalytics,
  ENHANCED_ORDER_STATUS,
  batchUpdateOrderStatus,
  useEnhancedOrderService,
  updateOrderStatusEnhanced
} from 'src/actions/mooly-chatbot/enhanced-order-service';

// ==========================================
// 📋 USAGE EXAMPLES
// ==========================================

/*
// 1. Basic Service Usage
import { useEnhancedOrderService } from './enhanced';

const {
  updateStatus,
  batchUpdateStatus,
  getAnalytics,
  getNextValidStatuses,
  getStatusColor,
  getStatusLabel,
  isLoading
} = useEnhancedOrderService();

// Update single order
await updateStatus(orderId, 'confirmed', {
  comment: 'Order confirmed',
  autoInventoryUpdate: true,
  notifyCustomer: true
});

// Batch update
await batchUpdateStatus(['id1', 'id2'], 'shipped', {
  comment: 'Batch shipped'
});

// 2. Component Usage
import { 
  EnhancedOrderStatusDialog,
  EnhancedOrderHistory,
  EnhancedBatchOperations,
  EnhancedOrderAnalytics
} from './enhanced';

// Status Dialog
<EnhancedOrderStatusDialog
  open={open}
  onClose={onClose}
  order={order}
  onSuccess={onSuccess}
/>

// Order History
<EnhancedOrderHistory
  orderId={orderId}
  showInventoryImpact={true}
/>

// Batch Operations
<EnhancedBatchOperations
  selectedOrders={selectedOrders}
  onSuccess={onSuccess}
  onClearSelection={onClearSelection}
/>

// Analytics
<EnhancedOrderAnalytics
  dateRange={dateRange}
  refreshTrigger={refreshTrigger}
/>

// 3. Full Page View
import { OrderListView } from './enhanced';

export default function OrdersPage() {
  return <OrderListView />;
}
*/
