'use client';

import PropTypes from 'prop-types';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Timeline from '@mui/lab/Timeline';
import TimelineDot from '@mui/lab/TimelineDot';
import TimelineItem from '@mui/lab/TimelineItem';
import Typography from '@mui/material/Typography';
import TimelineContent from '@mui/lab/TimelineContent';
import TimelineSeparator from '@mui/lab/TimelineSeparator';
import TimelineConnector from '@mui/lab/TimelineConnector';
import TimelineOppositeContent from '@mui/lab/TimelineOppositeContent';

import { fDate, fTime } from 'src/utils/format-time';

import { getOrderStatusInfo } from 'src/actions/mooly-chatbot/order-constants';

// ----------------------------------------------------------------------

export function OrderStatusHistory({ history = [] }) {
  const getStatusInfo = (status) => {
    const statusInfo = getOrderStatusInfo(status);
    // Đảm bảo color luôn là một trong các giá trị hợp lệ của MUI
    const validColors = ['primary', 'secondary', 'error', 'info', 'success', 'warning', 'grey'];
    const color =
      statusInfo?.color && validColors.includes(statusInfo.color) ? statusInfo.color : 'grey';
    return statusInfo ? { ...statusInfo, color } : { label: 'Không xác định', color: 'grey' };
  };

  return (
    <Card>
      <Stack spacing={3} sx={{ p: 3 }}>
        <Typography variant="h6">Lịch sử trạng thái</Typography>

        {history.length > 0 ? (
          <Timeline position="right">
            {history.map((item) => {
              const statusInfo = getStatusInfo(item.status);
              const prevStatusInfo = item.previousStatus
                ? getStatusInfo(item.previousStatus)
                : null;

              return (
                <TimelineItem key={item.id}>
                  <TimelineOppositeContent sx={{ m: 'auto 0' }}>
                    <Stack spacing={0.5}>
                      <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                        {fDate(item.createdAt)}
                      </Typography>
                      <Typography variant="caption" sx={{ color: 'text.disabled' }}>
                        {fTime(item.createdAt)}
                      </Typography>
                    </Stack>
                  </TimelineOppositeContent>

                  <TimelineSeparator>
                    <TimelineDot color={statusInfo.color} />
                    <TimelineConnector />
                  </TimelineSeparator>

                  <TimelineContent sx={{ py: '12px', px: 2 }}>
                    <Typography variant="subtitle2">
                      {prevStatusInfo
                        ? `Thay đổi từ "${prevStatusInfo.label}" sang "${statusInfo.label}"`
                        : `Đơn hàng được tạo với trạng thái "${statusInfo.label}"`}
                    </Typography>

                    {item.comment && (
                      <Typography variant="body2" sx={{ color: 'text.secondary', mt: 0.5 }}>
                        {item.comment}
                      </Typography>
                    )}

                    {item.userId && (
                      <Typography variant="caption" sx={{ color: 'text.disabled', mt: 0.5 }}>
                        Thực hiện bởi: {item.userId}
                      </Typography>
                    )}
                  </TimelineContent>
                </TimelineItem>
              );
            })}
          </Timeline>
        ) : (
          <Box sx={{ py: 3 }}>
            <Typography variant="body2" sx={{ color: 'text.secondary', textAlign: 'center' }}>
              Chưa có lịch sử trạng thái nào
            </Typography>
          </Box>
        )}
      </Stack>
    </Card>
  );
}

OrderStatusHistory.propTypes = {
  history: PropTypes.array,
};
