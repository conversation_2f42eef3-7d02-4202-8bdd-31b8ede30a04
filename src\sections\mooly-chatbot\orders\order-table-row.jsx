'use client';

import PropTypes from 'prop-types';
import { useBoolean, usePopover } from 'minimal-shared/hooks';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Avatar from '@mui/material/Avatar';
import MenuList from '@mui/material/MenuList';
import MenuItem from '@mui/material/MenuItem';
import TableRow from '@mui/material/TableRow';
import Checkbox from '@mui/material/Checkbox';
import TableCell from '@mui/material/TableCell';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import ListItemText from '@mui/material/ListItemText';

import { fCurrency } from 'src/utils/format-number';
import { fDate, fTime } from 'src/utils/format-time';

import { ORDER_STATUS_OPTIONS } from 'src/actions/mooly-chatbot/order-constants';

import { Label } from 'src/components/label';
import { Iconify } from 'src/components/iconify';
import { ConfirmDialog } from 'src/components/custom-dialog';
import { CustomPopover } from 'src/components/custom-popover';

// ----------------------------------------------------------------------

export function OrderTableRow({ row, selected, onSelectRow, onDeleteRow, onViewRow, onEditRow }) {
  const confirmDialog = useBoolean();
  const menuActions = usePopover();

  const getStatusInfo = (status) => {
    const statusOption = ORDER_STATUS_OPTIONS.find((option) => option.value === status);
    return statusOption || { label: 'Không xác định', color: 'default' };
  };

  const statusInfo = getStatusInfo(row.status);

  return (
    <>
      <TableRow hover selected={selected}>
        <TableCell padding="checkbox">
          <Checkbox
            checked={selected}
            onClick={onSelectRow}
            slotProps={{
              input: {
                id: `${row.id}-checkbox`,
                'aria-label': `${row.id} checkbox`,
              },
            }}
          />
        </TableCell>

        <TableCell>
          <Typography variant="subtitle2" noWrap>
            {row.orderNumber}
          </Typography>
        </TableCell>

        <TableCell>
          <Box sx={{ gap: 2, display: 'flex', alignItems: 'center' }}>
            <Avatar alt={row.customerName} src="/assets/images/avatar/avatar_default.jpg" />

            <Stack sx={{ typography: 'body2', flex: '1 1 auto', alignItems: 'flex-start' }}>
              <Box component="span">{row.customerName}</Box>

              <Box component="span" sx={{ color: 'text.disabled' }}>
                {row.customerPhone}
              </Box>
            </Stack>
          </Box>
        </TableCell>

        <TableCell>
          <ListItemText
            primary={fDate(row.createdAt)}
            secondary={fTime(row.createdAt)}
            slotProps={{
              primary: {
                noWrap: true,
                sx: { typography: 'body2' },
              },
              secondary: {
                sx: { mt: 0.5, typography: 'caption' },
              },
            }}
          />
        </TableCell>

        <TableCell align="center">
          <Typography variant="body2" sx={{ fontWeight: 600 }}>
            {fCurrency(row.totalAmount)}
          </Typography>
        </TableCell>

        <TableCell align="center">
          <Label
            variant="soft"
            color={statusInfo.color}
            sx={{
              textTransform: 'capitalize',
              minWidth: 'fit-content',
              whiteSpace: 'nowrap'
            }}
          >
            {statusInfo.label}
          </Label>
        </TableCell>

        <TableCell align="right" sx={{ minWidth: 120 }}>
          <Stack direction="row" alignItems="center" justifyContent="flex-end" spacing={0.5}>
            <IconButton
              color="default"
              onClick={menuActions.onOpen}
              sx={{
                minWidth: 32,
                width: 32,
                height: 32
              }}
            >
              <Iconify icon="eva:more-vertical-fill" />
            </IconButton>
          </Stack>
        </TableCell>
      </TableRow>

      <CustomPopover
        open={menuActions.open}
        anchorEl={menuActions.anchorEl}
        onClose={menuActions.onClose}
        slotProps={{ arrow: { placement: 'right-top' } }}
      >
        <MenuList>
          <MenuItem
            onClick={() => {
              onViewRow();
              menuActions.onClose();
            }}
          >
            <Iconify icon="solar:eye-bold" />
            Xem chi tiết
          </MenuItem>

          <MenuItem
            onClick={() => {
              onEditRow();
              menuActions.onClose();
            }}
          >
            <Iconify icon="solar:pen-bold" />
            Chỉnh sửa
          </MenuItem>

          <MenuItem
            onClick={() => {
              confirmDialog.onTrue();
              menuActions.onClose();
            }}
            sx={{ color: 'error.main' }}
          >
            <Iconify icon="solar:close-circle-bold" />
            Hủy đơn hàng
          </MenuItem>
        </MenuList>
      </CustomPopover>

      <ConfirmDialog
        open={confirmDialog.value}
        onClose={confirmDialog.onFalse}
        title="Hủy đơn hàng"
        content={
          <>
            Bạn có chắc chắn muốn hủy đơn hàng này?
            <br />
            <small style={{ color: '#666', marginTop: '8px', display: 'block' }}>
              Đơn hàng sẽ được chuyển sang trạng thái &quot;Đã hủy&quot; và tồn kho sẽ được hoàn lại.
            </small>
          </>
        }
        action={
          <Button
            variant="contained"
            color="error"
            onClick={() => {
              onDeleteRow();
              confirmDialog.onFalse();
            }}
          >
            Hủy đơn hàng
          </Button>
        }
      />
    </>
  );
}

OrderTableRow.propTypes = {
  onDeleteRow: PropTypes.func,
  onEditRow: PropTypes.func,
  onSelectRow: PropTypes.func,
  onViewRow: PropTypes.func,
  row: PropTypes.object,
  selected: PropTypes.bool,
};
