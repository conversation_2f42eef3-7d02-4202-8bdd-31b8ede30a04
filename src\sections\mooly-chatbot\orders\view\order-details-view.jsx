'use client';

import { useBoolean } from 'minimal-shared/hooks';
import { useMemo, useState, useCallback } from 'react';

import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import LoadingButton from '@mui/lab/LoadingButton';

import { paths } from 'src/routes/paths';
import { useParams, useRouter } from 'src/routes/hooks';

import { fCurrency } from 'src/utils/format-number';
import { fDate, fTime } from 'src/utils/format-time';

import { DashboardContent } from 'src/layouts/dashboard';
import { useOrder } from 'src/actions/mooly-chatbot/order-service';
import { useOrderMutations } from 'src/actions/mooly-chatbot/order-mutations';
import { ORDER_STATUS_OPTIONS } from 'src/actions/mooly-chatbot/order-constants';

import { Label } from 'src/components/label';
import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { EmptyContent } from 'src/components/empty-content';
import { ConfirmDialog } from 'src/components/custom-dialog';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';

import { OrderSummary } from '../order-summary';
import { OrderItemsList } from '../order-items-list';
import { OrderStatusHistory } from '../order-status-history';
import { OrderStatusUpdateDialog } from '../order-status-update-dialog';
import { OrderEditBasicDialog } from '../dialogs/order-edit-basic-dialog';
import { OrderEditItemsDialog } from '../dialogs/order-edit-items-dialog';
import { OrderEditAddressDialog } from '../dialogs/order-edit-address-dialog';
import { OrderEditPricingDialog } from '../dialogs/order-edit-pricing-dialog';
import { canEditAnySection, getEditPermissions } from '../utils/order-edit-permissions';
import { AddressValidationStatus } from '../components/address-validation-status';
import { OrderAddressSummary, OrderAddressChip } from '../components/order-address-summary';
import { formatFullAddress, validateAddressCompleteness } from '../utils/address-formatter';

// ----------------------------------------------------------------------

const TABS = [
  { value: 'details', label: 'Chi tiết đơn hàng' },
  { value: 'history', label: 'Lịch sử trạng thái' },
  { value: 'inventory', label: 'Thông tin kho hàng' },
];

// ----------------------------------------------------------------------

export default function OrderDetailsView() {
  const params = useParams();
  const { id } = params;

  const router = useRouter();

  const confirmDialog = useBoolean();
  const statusDialog = useBoolean();
  const editBasicDialog = useBoolean();
  const editAddressDialog = useBoolean();
  const editItemsDialog = useBoolean();
  const editPricingDialog = useBoolean();

  const [currentTab, setCurrentTab] = useState('details');

  // Lấy thông tin chi tiết đơn hàng
  const { order, orderItems, orderHistory, shippingAddress, billingAddress, isLoading, mutate } = useOrder(id);

  // Mutations
  const { isMutating } = useOrderMutations();

  // Edit permissions
  const editPermissions = order ? getEditPermissions(order.status) : {};
  const canEdit = order ? canEditAnySection(order.status) : false;

  // Address validation
  const addressValidation = useMemo(() => {
    if (!shippingAddress) return null;
    return validateAddressCompleteness(shippingAddress);
  }, [shippingAddress]);

  // Tính toán thông tin tổng quan
  const orderStats = useMemo(() => {
    if (!orderItems || orderItems.length === 0) {
      return {
        totalItems: 0,
        simpleProducts: 0,
        variableProducts: 0,
        totalQuantity: 0,
      };
    }

    const stats = orderItems.reduce(
      (acc, item) => {
        acc.totalItems += 1;
        acc.totalQuantity += item.quantity || 0;

        if (item.variantId) {
          acc.variableProducts += 1;
        } else {
          acc.simpleProducts += 1;
        }

        return acc;
      },
      { totalItems: 0, simpleProducts: 0, variableProducts: 0, totalQuantity: 0 }
    );

    return stats;
  }, [orderItems]);

  // Lấy thông tin trạng thái
  const statusInfo = useMemo(() => {
    if (!order?.status) return { label: 'Không xác định', color: 'default' };

    const { getOrderStatusInfo } = require('src/actions/mooly-chatbot/order-constants');
    const statusOption = getOrderStatusInfo(order.status);
    return statusOption || { label: 'Không xác định', color: 'default' };
  }, [order?.status]);

  const handleChangeTab = useCallback((_, newValue) => {
    setCurrentTab(newValue);
  }, []);

  const handleCancelOrder = useCallback(async () => {
    try {
      // Import cancelOrder function
      const { cancelOrder } = await import('src/actions/mooly-chatbot/order-service');

      // Gọi API hủy đơn hàng
      const result = await cancelOrder(id, 'Hủy đơn hàng từ trang chi tiết');

      if (result.success) {
        toast.success('Hủy đơn hàng thành công!');
        router.push(paths.dashboard.moolyChatbot.orders.root);
      } else {
        toast.error(result.error || 'Hủy đơn hàng thất bại!');
      }
    } catch (error) {
      console.error(error);
      toast.error('Hủy đơn hàng thất bại!');
    }
  }, [id, router]);

  const handleUpdateStatusSuccess = useCallback(
    () => {
      mutate();
      statusDialog.onFalse();
      toast.success('Cập nhật trạng thái đơn hàng thành công!');
    },
    [mutate, statusDialog]
  );

  // Header thông tin tổng quan
  const renderOrderHeader = (
    <Card sx={{ p: 3, mb: 3 }}>
      <Stack spacing={3}>
        {/* Thông tin cơ bản */}
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          justifyContent="space-between"
          alignItems={{ xs: 'flex-start', sm: 'center' }}
          spacing={2}
        >
          <Stack spacing={1}>
            <Typography variant="h4">
              Đơn hàng #{order?.orderNumber}
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              Tạo lúc: {order?.createdAt ? `${fDate(order.createdAt)} ${fTime(order.createdAt)}` : 'N/A'}
            </Typography>
          </Stack>

          <Stack direction="row" spacing={1} alignItems="center">
            <Label variant="soft" color={statusInfo.color} sx={{ textTransform: 'capitalize' }}>
              {statusInfo.label}
            </Label>
            <Typography variant="h5" sx={{ color: 'primary.main' }}>
              {fCurrency(order?.totalAmount || 0)}
            </Typography>
          </Stack>
        </Stack>

        <Divider sx={{ borderStyle: 'dashed' }} />

        {/* Thống kê sản phẩm và địa chỉ */}
        <Stack direction={{ xs: 'column', md: 'row' }} spacing={3} justifyContent="space-between">
          {/* Thống kê sản phẩm */}
          <Stack direction="row" spacing={3} flexWrap="wrap">
            <Stack direction="row" spacing={1} alignItems="center">
              <Chip
                label={`${orderStats.totalItems} sản phẩm`}
                variant="outlined"
                size="small"
                color="default"
              />
              <Chip
                label={`${orderStats.totalQuantity} món`}
                variant="outlined"
                size="small"
                color="primary"
              />
            </Stack>

            {orderStats.simpleProducts > 0 && (
              <Chip
                label={`${orderStats.simpleProducts} sản phẩm đơn giản`}
                variant="soft"
                size="small"
                color="primary"
              />
            )}

            {orderStats.variableProducts > 0 && (
              <Chip
                label={`${orderStats.variableProducts} sản phẩm biến thể`}
                variant="soft"
                size="small"
                color="info"
              />
            )}
          </Stack>

          {/* Thông tin địa chỉ ngắn gọn */}
          <OrderAddressSummary
            shippingAddress={shippingAddress}
            billingAddress={billingAddress}
            onEditAddress={editPermissions.address ? editAddressDialog.onTrue : null}
            showEditButton={false}
            compact
          />
        </Stack>
      </Stack>
    </Card>
  );

  const renderContent = (
    <Stack spacing={3}>
      {renderOrderHeader}

      <Card sx={{ p: 3 }}>
        <Tabs
          value={currentTab}
          onChange={handleChangeTab}
          sx={{
            px: 2,
            mb: 3,
            boxShadow: (theme) => `inset 0 -2px 0 0 ${theme.vars.palette.divider}`,
          }}
        >
          {TABS.map((tab) => (
            <Tab key={tab.value} value={tab.value} label={tab.label} />
          ))}
        </Tabs>

        {currentTab === 'details' && (
          <Stack spacing={3}>
            <OrderSummary
              order={order}
              shippingAddress={shippingAddress}
              billingAddress={billingAddress}
              onEditBasic={editBasicDialog.onTrue}
              onEditAddress={editAddressDialog.onTrue}
              onEditPricing={editPricingDialog.onTrue}
            />
            <OrderItemsList
              items={orderItems}
              orderStatus={order?.status}
              onEditItems={editItemsDialog.onTrue}
              onImagesLoaded={() => {
                // Khi hình ảnh đã được tải xong, có thể thực hiện các hành động bổ sung ở đây
              }}
            />
          </Stack>
        )}

        {currentTab === 'history' && <OrderStatusHistory history={orderHistory} />}

        {currentTab === 'inventory' && (
          <Stack spacing={3}>
            <Typography variant="h6">Thông tin kho hàng</Typography>

            {orderItems && orderItems.length > 0 ? (
              <Stack spacing={2}>
                {orderItems.map((item) => (
                  <Card key={item.id} sx={{ p: 3 }}>
                    <Stack spacing={2}>
                      <Stack direction="row" justifyContent="space-between" alignItems="center">
                        <Typography variant="subtitle1">{item.name}</Typography>
                        <Chip
                          label={item.variantId ? 'Biến thể' : 'Sản phẩm đơn giản'}
                          variant="soft"
                          size="small"
                          color={item.variantId ? 'info' : 'primary'}
                        />
                      </Stack>

                      <Stack direction="row" spacing={3}>
                        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                          SKU: {item.sku || 'N/A'}
                        </Typography>
                        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                          Số lượng đặt: {item.quantity}
                        </Typography>
                        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                          Đơn giá: {fCurrency(item.unitPrice)}
                        </Typography>
                      </Stack>

                      {item.variantId && item.variantDetail && (
                        <Stack spacing={1}>
                          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                            Thông tin biến thể:
                          </Typography>
                          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                            Tên: {item.variantDetail.name}
                          </Typography>
                          {item.variantDetail.attributes && (
                            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                              Thuộc tính: {Object.entries(item.variantDetail.attributes)
                                .map(([key, value]) => `${key}: ${value}`)
                                .join(', ')}
                            </Typography>
                          )}
                          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                            Tồn kho hiện tại: {item.variantDetail.stockQuantity || 0}
                          </Typography>
                        </Stack>
                      )}

                      {!item.variantId && item.productDetail && (
                        <Stack spacing={1}>
                          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                            Thông tin sản phẩm:
                          </Typography>
                          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                            Loại: {item.productDetail.type}
                          </Typography>
                          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                            Tồn kho hiện tại: {item.productDetail.stockQuantity || 0}
                          </Typography>
                        </Stack>
                      )}
                    </Stack>
                  </Card>
                ))}
              </Stack>
            ) : (
              <Typography variant="body2" sx={{ color: 'text.secondary', textAlign: 'center', py: 3 }}>
                Không có thông tin sản phẩm
              </Typography>
            )}
          </Stack>
        )}
      </Card>
    </Stack>
  );

  return (
    <DashboardContent>
      <Container maxWidth="lg">
        <CustomBreadcrumbs
          heading={`Chi tiết đơn hàng #${order?.orderNumber || id}`}
          links={[
            { name: 'Dashboard', href: paths.dashboard.root },
            { name: 'Mooly Chatbot', href: paths.dashboard.moolyChatbot.root },
            { name: 'Đơn hàng', href: paths.dashboard.moolyChatbot.orders.root },
            { name: order?.orderNumber || `#${id.slice(-8)}` },
          ]}
          action={
            <Stack direction="row" spacing={1}>
              {/* Quick edit address chip */}
              {editPermissions.address && (
                <OrderAddressChip
                  address={shippingAddress}
                  onClick={editAddressDialog.onTrue}
                />
              )}

              {canEdit && (
                <Button
                  color="inherit"
                  variant="outlined"
                  startIcon={<Iconify icon="solar:pen-bold" />}
                  onClick={() => {
                    // Mở dialog edit đầu tiên có thể edit
                    if (editPermissions.basicInfo) {
                      editBasicDialog.onTrue();
                    } else if (editPermissions.address) {
                      editAddressDialog.onTrue();
                    } else if (editPermissions.items) {
                      editItemsDialog.onTrue();
                    } else if (editPermissions.pricing) {
                      editPricingDialog.onTrue();
                    }
                  }}
                  disabled={!order}
                >
                  Chỉnh sửa
                </Button>
              )}

              {editPermissions.status && (
                <LoadingButton
                  color="primary"
                  variant="contained"
                  startIcon={<Iconify icon="solar:file-check-bold" />}
                  onClick={statusDialog.onTrue}
                  loading={isMutating}
                  disabled={!order}
                >
                  Cập nhật trạng thái
                </LoadingButton>
              )}

              <Button
                color="error"
                variant="outlined"
                startIcon={<Iconify icon="solar:close-circle-bold" />}
                onClick={confirmDialog.onTrue}
                disabled={!order}
              >
                Hủy đơn hàng
              </Button>
            </Stack>
          }
          sx={{ mb: { xs: 3, md: 5 } }}
        />

        {isLoading ? (
          <Box sx={{ py: 5 }}>
            <Typography variant="body2" sx={{ textAlign: 'center' }}>
              Đang tải...
            </Typography>
          </Box>
        ) : !order ? (
          <EmptyContent
            filled
            title="Không tìm thấy đơn hàng"
            description="Đơn hàng không tồn tại hoặc đã bị xóa"
            action={
              <Button
                variant="contained"
                onClick={() => router.push(paths.dashboard.moolyChatbot.orders.root)}
                startIcon={<Iconify icon="eva:arrow-back-fill" />}
              >
                Quay lại danh sách
              </Button>
            }
          />
        ) : (
          renderContent
        )}
      </Container>

      <OrderStatusUpdateDialog
        open={statusDialog.value}
        onClose={statusDialog.onFalse}
        orderId={id}
        currentStatus={order?.status}
        onSuccess={handleUpdateStatusSuccess}
      />

      <ConfirmDialog
        open={confirmDialog.value}
        onClose={confirmDialog.onFalse}
        title="Hủy đơn hàng"
        content={
          <>
            Bạn có chắc chắn muốn hủy đơn hàng này?
            <br />
            <small style={{ color: '#666', marginTop: '8px', display: 'block' }}>
              Đơn hàng sẽ được chuyển sang trạng thái &quot;Đã hủy&quot; và tồn kho sẽ được hoàn lại.
            </small>
          </>
        }
        action={
          <Button
            variant="contained"
            color="error"
            onClick={() => {
              handleCancelOrder();
              confirmDialog.onFalse();
            }}
          >
            Hủy đơn hàng
          </Button>
        }
      />

      {/* Edit Dialogs */}
      <OrderEditBasicDialog
        open={editBasicDialog.value}
        onClose={editBasicDialog.onFalse}
        order={order}
        onSuccess={() => {
          mutate();
          editBasicDialog.onFalse();
        }}
      />

      <OrderEditAddressDialog
        open={editAddressDialog.value}
        onClose={editAddressDialog.onFalse}
        order={order}
        shippingAddress={shippingAddress}
        billingAddress={billingAddress}
        onSuccess={() => {
          mutate();
          editAddressDialog.onFalse();
        }}
      />

      <OrderEditPricingDialog
        open={editPricingDialog.value}
        onClose={editPricingDialog.onFalse}
        order={order}
        onSuccess={() => {
          mutate();
          editPricingDialog.onFalse();
        }}
      />

      <OrderEditItemsDialog
        open={editItemsDialog.value}
        onClose={editItemsDialog.onFalse}
        order={order}
        orderItems={orderItems}
        onSuccess={() => {
          mutate();
          editItemsDialog.onFalse();
        }}
      />
    </DashboardContent>
  );
}
