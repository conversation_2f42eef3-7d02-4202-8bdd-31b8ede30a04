'use client';

import { useState, useCallback, useEffect } from 'react';

import {
  Box,
  Card,
  Stack,
  Button,
  Typography,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControlLabel,
  Switch,
  Tooltip,
  Divider,
  Alert,
  Autocomplete,
  keyframes
} from '@mui/material';

import { useBoolean } from 'src/hooks/use-boolean';

import { Iconify } from 'src/components/iconify';
import { Label } from 'src/components/label';

import { useWorkflowDetail } from 'src/actions/mooly-chatbot/use-custom-workflow';
import { ORDER_STATUS_OPTIONS, getOrderStatusInfo } from 'src/actions/mooly-chatbot/order-constants';
import { PRODUCT_TYPE_LABELS, getBusinessFlow } from 'src/actions/mooly-chatbot/order-status-business-rules';

// ----------------------------------------------------------------------

const bounceAnimation = keyframes`
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
`;

const spinAnimation = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;

// ----------------------------------------------------------------------

export default function WorkflowCanvas({ workflow, onUpdate }) {
  const [selectedStage, setSelectedStage] = useState(null);
  
  // Dialogs
  const addStageDialog = useBoolean();
  const editStageDialog = useBoolean();
  
  // Workflow detail hook
  const {
    workflow: detailWorkflow,
    stages,
    sortedStages,
    loading,
    createStage,
    updateStage,
    deleteStage,
    refresh
  } = useWorkflowDetail(workflow?.id);

  const handleCreateStage = useCallback(async (stageData) => {
    const result = await createStage(stageData);
    if (result.success) {
      addStageDialog.onFalse();
      refresh();
      if (onUpdate) onUpdate(detailWorkflow);
    }
    return result;
  }, [createStage, addStageDialog, refresh, onUpdate, detailWorkflow]);

  const handleUpdateStage = useCallback(async (stageId, updateData) => {
    const result = await updateStage(stageId, updateData);
    if (result.success) {
      editStageDialog.onFalse();
      setSelectedStage(null);
      refresh();
      if (onUpdate) onUpdate(detailWorkflow);
    }
    return result;
  }, [updateStage, editStageDialog, refresh, onUpdate, detailWorkflow]);

  const handleDeleteStage = useCallback(async (stageId) => {
    const result = await deleteStage(stageId);
    if (result.success) {
      setSelectedStage(null);
      refresh();
      if (onUpdate) onUpdate(detailWorkflow);
    }
    return result;
  }, [deleteStage, refresh, onUpdate, detailWorkflow]);

  const handleStageReorder = useCallback(async (fromIndex, toIndex) => {
    if (fromIndex === toIndex) return;

    try {
      const newStages = [...sortedStages];
      const [movedStage] = newStages.splice(fromIndex, 1);
      newStages.splice(toIndex, 0, movedStage);

      // Update sort_order for all affected stages
      const updates = newStages.map((stage, index) => ({
        id: stage.id,
        sort_order: index + 1
      }));

      // Batch update stages
      for (const update of updates) {
        await updateStage(update.id, { sort_order: update.sort_order });
      }

      refresh();
      if (onUpdate) onUpdate(detailWorkflow);
    } catch (error) {
      console.error('Error reordering stages:', error);
    }
  }, [sortedStages, updateStage, refresh, onUpdate, detailWorkflow]);

  const renderWorkflowInfo = (
    <Box sx={{ mb: 3 }}>
      <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
        <Typography variant="h6">
          {workflow.name}
        </Typography>
        <Stack direction="row" spacing={1}>
          {workflow.is_default && (
            <Label color="success">Mặc định</Label>
          )}
          {workflow.is_active ? (
            <Label color="success">Đang hoạt động</Label>
          ) : (
            <Label color="error">Tạm dừng</Label>
          )}
        </Stack>
      </Stack>
      
      {workflow.description && (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {workflow.description}
        </Typography>
      )}
      
      <Stack direction="row" spacing={2} alignItems="center">
        <Typography variant="body2" color="text.secondary">
          <strong>{sortedStages.length}</strong> bước trong quy trình
        </Typography>
        <Divider orientation="vertical" flexItem />
        <Button
          size="small"
          startIcon={<Iconify icon="solar:add-circle-bold" />}
          onClick={addStageDialog.onTrue}
        >
          Thêm bước
        </Button>
      </Stack>
    </Box>
  );

  const renderStageFlow = (
    <Box>
      <Typography variant="subtitle2" sx={{ mb: 2 }}>
        Luồng quy trình
      </Typography>
      
      <Stack spacing={2}>
        {sortedStages.map((stage, index) => (
          <Box key={stage.id}>
            <StageCard
              stage={stage}
              index={index}
              totalStages={sortedStages.length}
              isSelected={selectedStage?.id === stage.id}
              onClick={() => setSelectedStage(stage)}
              onEdit={() => {
                setSelectedStage(stage);
                editStageDialog.onTrue();
              }}
              onDelete={() => handleDeleteStage(stage.id)}
              onReorder={handleStageReorder}
            />

            {index < sortedStages.length - 1 && (
              <Box display="flex" justifyContent="center" py={1}>
                <Iconify
                  icon="solar:arrow-down-bold"
                  sx={{
                    color: 'primary.main',
                    animation: `${bounceAnimation} 2s infinite`
                  }}
                />
              </Box>
            )}
          </Box>
        ))}
        
        {sortedStages.length === 0 && (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            py={6}
            color="text.secondary"
          >
            <Iconify icon="solar:document-add-bold" width={48} sx={{ mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              Chưa có bước nào
            </Typography>
            <Typography variant="body2" textAlign="center" sx={{ mb: 3 }}>
              Thêm các bước để tạo quy trình xử lý đơn hàng
            </Typography>
            <Button
              variant="contained"
              startIcon={<Iconify icon="solar:add-circle-bold" />}
              onClick={addStageDialog.onTrue}
            >
              Thêm bước đầu tiên
            </Button>
          </Box>
        )}
      </Stack>
    </Box>
  );

  return (
    <>
      <Box>
        {renderWorkflowInfo}
        {renderStageFlow}
      </Box>

      {/* Add Stage Dialog */}
      <StageFormDialog
        open={addStageDialog.value}
        onClose={addStageDialog.onFalse}
        onSubmit={handleCreateStage}
        title="Thêm bước mới"
        loading={loading}
        existingStages={sortedStages}
        workflow={workflow}
      />

      {/* Edit Stage Dialog */}
      <StageFormDialog
        open={editStageDialog.value}
        onClose={() => {
          editStageDialog.onFalse();
          setSelectedStage(null);
        }}
        onSubmit={(data) => handleUpdateStage(selectedStage?.id, data)}
        title="Chỉnh sửa bước"
        initialData={selectedStage}
        loading={loading}
        existingStages={sortedStages}
        workflow={workflow}
      />
    </>
  );
}

// ----------------------------------------------------------------------

function StageCard({ stage, isSelected, onClick, onEdit, onDelete, onReorder, index, totalStages }) {
  const statusInfo = getOrderStatusInfo(stage.status_code);
  const [isDragging, setIsDragging] = useState(false);

  const handleDragStart = useCallback((event) => {
    setIsDragging(true);
    event.dataTransfer.setData('text/plain', JSON.stringify({ id: stage.id, index }));
    event.dataTransfer.effectAllowed = 'move';
  }, [stage.id, index]);

  const handleDragEnd = useCallback(() => {
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const handleDrop = useCallback((event) => {
    event.preventDefault();

    try {
      const draggedData = JSON.parse(event.dataTransfer.getData('text/plain'));
      if (draggedData.id !== stage.id && onReorder) {
        onReorder(draggedData.index, index);
      }
    } catch (error) {
      console.error('Error handling drop:', error);
    }
  }, [stage.id, index, onReorder]);

  const getStageIcon = () => {
    if (stage.is_start_stage) return 'solar:play-circle-bold';
    if (stage.is_end_stage) return 'solar:stop-circle-bold';
    if (stage.requires_payment) return 'solar:card-bold';
    if (stage.requires_inventory) return 'solar:box-bold';
    if (stage.auto_transition) return 'solar:automation-bold';
    return 'solar:settings-bold';
  };

  const getStageColor = () => {
    if (stage.is_start_stage) return 'success';
    if (stage.is_end_stage) return 'error';
    if (stage.requires_payment) return 'warning';
    return 'primary';
  };

  return (
    <Card
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
      variant={isSelected ? 'elevation' : 'outlined'}
      sx={{
        p: 2,
        cursor: isDragging ? 'grabbing' : 'grab',
        border: isSelected ? 2 : 1,
        borderColor: isSelected ? 'primary.main' : 'divider',
        opacity: isDragging ? 0.5 : 1,
        transform: isDragging ? 'rotate(5deg)' : 'none',
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          borderColor: 'primary.main',
          bgcolor: 'action.hover',
          transform: isDragging ? 'rotate(5deg)' : 'translateY(-2px)',
          boxShadow: 2
        }
      }}
      onClick={onClick}
    >
      <Stack direction="row" alignItems="center" justifyContent="space-between">
        <Stack direction="row" alignItems="center" spacing={2} flex={1}>
          {/* Stage Number & Icon */}
          <Box
            sx={{
              width: 40,
              height: 40,
              borderRadius: '50%',
              bgcolor: `${getStageColor()}.lighter`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative'
            }}
          >
            <Typography variant="caption" fontWeight="bold" color={`${getStageColor()}.main`}>
              {index + 1}
            </Typography>
            <Box
              sx={{
                position: 'absolute',
                top: -4,
                right: -4,
                width: 16,
                height: 16,
                borderRadius: '50%',
                bgcolor: `${getStageColor()}.main`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <Iconify icon={getStageIcon()} width={10} sx={{ color: 'white' }} />
            </Box>
          </Box>

          {/* Stage Info */}
          <Box flex={1}>
            <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1 }}>
              <Typography variant="subtitle2" fontWeight="medium">
                {stage.name}
              </Typography>
              <Chip
                label={statusInfo?.label || stage.status_code}
                size="small"
                color={statusInfo?.color || 'default'}
                variant="outlined"
              />
            </Stack>

            <Stack direction="row" spacing={0.5} flexWrap="wrap" useFlexGap>
              {stage.is_start_stage && (
                <Label color="success" size="small" startIcon={<Iconify icon="solar:play-circle-bold" width={12} />}>
                  Bắt đầu
                </Label>
              )}
              {stage.is_end_stage && (
                <Label color="error" size="small" startIcon={<Iconify icon="solar:stop-circle-bold" width={12} />}>
                  Kết thúc
                </Label>
              )}
              {stage.requires_payment && (
                <Label color="warning" size="small" startIcon={<Iconify icon="solar:card-bold" width={12} />}>
                  Thanh toán
                </Label>
              )}
              {stage.requires_inventory && (
                <Label color="info" size="small" startIcon={<Iconify icon="solar:box-bold" width={12} />}>
                  Kho hàng
                </Label>
              )}
              {stage.auto_transition && (
                <Label color="secondary" size="small" startIcon={<Iconify icon="solar:automation-bold" width={12} />}>
                  Tự động
                </Label>
              )}
              {stage.notify_customer && (
                <Label color="primary" size="small" startIcon={<Iconify icon="solar:bell-bold" width={12} />}>
                  Thông báo
                </Label>
              )}
            </Stack>

            {stage.description && (
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                {stage.description}
              </Typography>
            )}
          </Box>
        </Stack>

        {/* Actions */}
        <Stack direction="row" spacing={0.5}>
          <Tooltip title="Kéo để sắp xếp lại">
            <IconButton size="small" sx={{ cursor: 'grab' }}>
              <Iconify icon="solar:sort-vertical-bold" />
            </IconButton>
          </Tooltip>

          <Tooltip title="Chỉnh sửa">
            <IconButton size="small" onClick={(e) => { e.stopPropagation(); onEdit(); }}>
              <Iconify icon="solar:pen-bold" />
            </IconButton>
          </Tooltip>

          <Tooltip title="Xóa">
            <IconButton
              size="small"
              color="error"
              onClick={(e) => { e.stopPropagation(); onDelete(); }}
            >
              <Iconify icon="solar:trash-bin-trash-bold" />
            </IconButton>
          </Tooltip>
        </Stack>
      </Stack>
    </Card>
  );
}

// ----------------------------------------------------------------------

function StageFormDialog({
  open,
  onClose,
  onSubmit,
  title,
  initialData = null,
  loading = false,
  existingStages = [],
  workflow = null
}) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    status_code: '',
    color: 'primary',
    sort_order: existingStages.length + 1,
    is_start_stage: false,
    is_end_stage: false,
    requires_payment: false,
    requires_inventory: false,
    auto_transition: false,
    notify_customer: true,
    notify_admin: false,
    ...initialData
  });

  const [validationErrors, setValidationErrors] = useState({});
  const [suggestions, setSuggestions] = useState([]);

  // Generate smart suggestions dựa trên product type và existing stages
  const generateStageSuggestions = useCallback(() => {
    if (!workflow?.product_type) return [];

    const businessFlow = getBusinessFlow(workflow.product_type);
    const existingStatusCodes = existingStages.map(s => s.status_code);

    // Lấy các status codes chưa được sử dụng theo business flow
    const availableStatuses = businessFlow.filter(statusCode =>
      !existingStatusCodes.includes(statusCode)
    );

    // Tạo suggestions với smart defaults
    return availableStatuses.slice(0, 5).map(statusCode => {
      const statusInfo = getOrderStatusInfo(statusCode);
      return {
        status_code: statusCode,
        name: statusInfo?.label || statusCode,
        description: statusInfo?.description || `Bước ${statusInfo?.label || statusCode} trong quy trình`,
        color: statusInfo?.color || 'primary',
        requires_payment: ['paid'].includes(statusCode),
        requires_inventory: ['packaging', 'shipping'].includes(statusCode),
        is_start_stage: statusCode === 'pending' && existingStages.length === 0,
        is_end_stage: ['completed', 'delivered'].includes(statusCode),
        auto_transition: ['confirmed', 'paid'].includes(statusCode),
        sort_order: existingStages.length + 1
      };
    });
  }, [workflow, existingStages]);

  // Load suggestions khi mở dialog
  useEffect(() => {
    if (open && !initialData) {
      setSuggestions(generateStageSuggestions());
    }
  }, [open, initialData, generateStageSuggestions]);

  // Apply suggestion
  const applySuggestion = useCallback((suggestion) => {
    setFormData(prev => ({
      ...prev,
      ...suggestion
    }));
    setValidationErrors({});
  }, []);

  // Validate form
  const validateForm = useCallback(() => {
    const errors = {};

    if (!formData.name.trim()) {
      errors.name = 'Tên bước không được để trống';
    }

    if (!formData.status_code) {
      errors.status_code = 'Vui lòng chọn trạng thái';
    }

    // Check duplicate status code
    const isDuplicate = existingStages.some(stage =>
      stage.status_code === formData.status_code &&
      (!initialData || stage.id !== initialData.id)
    );

    if (isDuplicate) {
      errors.status_code = 'Trạng thái này đã được sử dụng';
    }

    // Validate start/end stage logic
    if (formData.is_start_stage) {
      const hasOtherStartStage = existingStages.some(stage =>
        stage.is_start_stage && (!initialData || stage.id !== initialData.id)
      );
      if (hasOtherStartStage) {
        errors.is_start_stage = 'Chỉ có thể có một bước bắt đầu';
      }
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [formData, existingStages, initialData]);

  const handleSubmit = useCallback(async () => {
    if (!validateForm()) return;

    const result = await onSubmit(formData);
    if (result.success) {
      setFormData({
        name: '',
        description: '',
        status_code: '',
        color: 'primary',
        sort_order: existingStages.length + 1,
        is_start_stage: false,
        is_end_stage: false,
        requires_payment: false,
        requires_inventory: false,
        auto_transition: false,
        notify_customer: true,
        notify_admin: false
      });
      setValidationErrors({});
    }
  }, [formData, onSubmit, existingStages.length, validateForm]);

  const handleChange = useCallback((field) => (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  // Lọc các status codes chưa được sử dụng
  const availableStatuses = ORDER_STATUS_OPTIONS.filter(status => 
    !existingStages.some(stage => stage.status_code === status.value) ||
    (initialData && initialData.status_code === status.value)
  );

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Stack direction="row" alignItems="center" spacing={1}>
          <Iconify icon="solar:settings-bold" />
          <Typography variant="h6">{title}</Typography>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          {/* Smart Suggestions */}
          {!initialData && suggestions.length > 0 && (
            <Box>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Gợi ý bước tiếp theo cho {PRODUCT_TYPE_LABELS[workflow?.product_type]}:
              </Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                {suggestions.map((suggestion, index) => (
                  <Chip
                    key={index}
                    label={suggestion.name}
                    variant="outlined"
                    size="small"
                    onClick={() => applySuggestion(suggestion)}
                    sx={{ mb: 1 }}
                    icon={<Iconify icon="solar:magic-stick-3-bold" />}
                  />
                ))}
              </Stack>
            </Box>
          )}

          {/* Business Flow Info */}
          {workflow?.product_type && (
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>Quy trình chuẩn cho {PRODUCT_TYPE_LABELS[workflow.product_type]}:</strong>
                {' '}
                {getBusinessFlow(workflow.product_type).map((status, index, array) => (
                  <span key={status}>
                    {getOrderStatusInfo(status)?.label || status}
                    {index < array.length - 1 ? ' → ' : ''}
                  </span>
                ))}
              </Typography>
            </Alert>
          )}

          <Stack direction="row" spacing={2}>
            <TextField
              label="Tên bước"
              value={formData.name}
              onChange={handleChange('name')}
              fullWidth
              required
              error={!!validationErrors.name}
              helperText={validationErrors.name || 'Tên mô tả cho bước này trong quy trình'}
            />

            <TextField
              label="Trạng thái"
              value={formData.status_code}
              onChange={handleChange('status_code')}
              select
              fullWidth
              required
              error={!!validationErrors.status_code}
              helperText={validationErrors.status_code || 'Trạng thái kỹ thuật của đơn hàng'}
            >
              {availableStatuses.map((status) => (
                <MenuItem key={status.value} value={status.value}>
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Chip
                      label={status.label}
                      size="small"
                      color={status.color}
                      variant="outlined"
                    />
                    <Typography variant="caption" color="text.secondary">
                      {status.description}
                    </Typography>
                  </Stack>
                </MenuItem>
              ))}
            </TextField>
          </Stack>
          
          <TextField
            label="Mô tả"
            value={formData.description}
            onChange={handleChange('description')}
            fullWidth
            multiline
            rows={2}
          />
          
          <Stack direction="row" spacing={2}>
            <TextField
              label="Thứ tự"
              value={formData.sort_order}
              onChange={handleChange('sort_order')}
              type="number"
              inputProps={{ min: 1 }}
            />
            
            <TextField
              label="Màu sắc"
              value={formData.color}
              onChange={handleChange('color')}
              select
            >
              {['primary', 'secondary', 'info', 'success', 'warning', 'error'].map((color) => (
                <MenuItem key={color} value={color}>
                  <Chip label={color} color={color} size="small" />
                </MenuItem>
              ))}
            </TextField>
          </Stack>
          
          <Stack spacing={2}>
            <Typography variant="subtitle2">Cấu hình bước</Typography>
            
            <Stack direction="row" spacing={2} flexWrap="wrap">
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.is_start_stage}
                    onChange={handleChange('is_start_stage')}
                  />
                }
                label="Bước bắt đầu"
              />
              {validationErrors.is_start_stage && (
                <Typography variant="caption" color="error">
                  {validationErrors.is_start_stage}
                </Typography>
              )}

              <FormControlLabel
                control={
                  <Switch
                    checked={formData.is_end_stage}
                    onChange={handleChange('is_end_stage')}
                  />
                }
                label="Bước kết thúc"
              />
            </Stack>
            
            <Stack direction="row" spacing={2} flexWrap="wrap">
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.requires_payment}
                    onChange={handleChange('requires_payment')}
                  />
                }
                label="Yêu cầu thanh toán"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.requires_inventory}
                    onChange={handleChange('requires_inventory')}
                  />
                }
                label="Yêu cầu kho hàng"
              />
            </Stack>
            
            <Stack direction="row" spacing={2} flexWrap="wrap">
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.notify_customer}
                    onChange={handleChange('notify_customer')}
                  />
                }
                label="Thông báo khách hàng"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.notify_admin}
                    onChange={handleChange('notify_admin')}
                  />
                }
                label="Thông báo admin"
              />
            </Stack>
          </Stack>
        </Stack>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>Hủy</Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={!formData.name.trim() || !formData.status_code || loading}
        >
          {initialData ? 'Cập nhật' : 'Thêm bước'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
