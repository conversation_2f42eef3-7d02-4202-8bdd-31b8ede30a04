'use client';

import { Box, Stack, Typography, Alert, LinearProgress, Chip } from '@mui/material';

import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

export default function WorkflowStatusIndicator({ 
  loading = false, 
  error = null, 
  success = null,
  workflow = null,
  showProgress = false,
  progressValue = 0
}) {
  if (loading) {
    return (
      <Box sx={{ py: 2 }}>
        <Stack spacing={2} alignItems="center">
          <Iconify 
            icon="solar:refresh-bold" 
            width={32} 
            sx={{ 
              animation: 'spin 1s linear infinite',
              color: 'primary.main'
            }} 
          />
          <Typography variant="body2" color="text.secondary">
            Đang xử lý...
          </Typography>
          {showProgress && (
            <Box sx={{ width: '100%', maxWidth: 300 }}>
              <LinearProgress 
                variant={progressValue > 0 ? 'determinate' : 'indeterminate'} 
                value={progressValue}
                sx={{ height: 6, borderRadius: 3 }}
              />
              {progressValue > 0 && (
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block', textAlign: 'center' }}>
                  {Math.round(progressValue)}%
                </Typography>
              )}
            </Box>
          )}
        </Stack>
      </Box>
    );
  }

  if (error) {
    const getErrorIcon = (errorCode) => {
      switch (errorCode) {
        case 'VALIDATION_ERROR':
          return 'solar:shield-warning-bold';
        case 'DUPLICATE_NAME':
          return 'solar:copy-bold';
        case 'PERMISSION_DENIED':
          return 'solar:lock-bold';
        case 'NETWORK_ERROR':
          return 'solar:wifi-off-bold';
        default:
          return 'solar:danger-bold';
      }
    };

    const getErrorSeverity = (errorCode) => {
      switch (errorCode) {
        case 'VALIDATION_ERROR':
        case 'DUPLICATE_NAME':
          return 'warning';
        case 'PERMISSION_DENIED':
          return 'error';
        case 'NETWORK_ERROR':
          return 'info';
        default:
          return 'error';
      }
    };

    return (
      <Alert 
        severity={getErrorSeverity(error.code)} 
        icon={<Iconify icon={getErrorIcon(error.code)} />}
        sx={{ mb: 2 }}
      >
        <Typography variant="subtitle2" gutterBottom>
          {error.code === 'VALIDATION_ERROR' && 'Lỗi xác thực dữ liệu'}
          {error.code === 'DUPLICATE_NAME' && 'Tên đã tồn tại'}
          {error.code === 'PERMISSION_DENIED' && 'Không có quyền truy cập'}
          {error.code === 'NETWORK_ERROR' && 'Lỗi kết nối'}
          {!error.code && 'Có lỗi xảy ra'}
        </Typography>
        <Typography variant="body2">
          {error.message || error}
        </Typography>
      </Alert>
    );
  }

  if (success) {
    return (
      <Alert 
        severity="success" 
        icon={<Iconify icon="solar:check-circle-bold" />}
        sx={{ mb: 2 }}
      >
        <Typography variant="body2">
          {success.message || success}
        </Typography>
      </Alert>
    );
  }

  if (workflow) {
    return (
      <Box sx={{ py: 1 }}>
        <Stack direction="row" spacing={2} alignItems="center">
          <Iconify 
            icon="solar:document-text-bold" 
            sx={{ color: 'primary.main' }} 
          />
          <Box flex={1}>
            <Typography variant="subtitle2">
              {workflow.name}
            </Typography>
            <Stack direction="row" spacing={1} sx={{ mt: 0.5 }}>
              <Chip 
                label={`${workflow.stages?.length || 0} bước`} 
                size="small" 
                color="info" 
                variant="outlined" 
              />
              {workflow.is_default && (
                <Chip label="Mặc định" size="small" color="success" />
              )}
              {workflow.is_active ? (
                <Chip label="Hoạt động" size="small" color="success" variant="outlined" />
              ) : (
                <Chip label="Tạm dừng" size="small" color="error" variant="outlined" />
              )}
            </Stack>
          </Box>
        </Stack>
      </Box>
    );
  }

  return null;
}

// ----------------------------------------------------------------------

export function WorkflowProgressIndicator({ 
  currentStep = 0, 
  totalSteps = 0, 
  stepLabels = [],
  showLabels = true 
}) {
  if (totalSteps === 0) return null;

  const progress = (currentStep / totalSteps) * 100;

  return (
    <Box sx={{ py: 2 }}>
      <Stack spacing={2}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography variant="body2" color="text.secondary">
            Tiến độ
          </Typography>
          <Typography variant="body2" color="primary.main" fontWeight="medium">
            {currentStep}/{totalSteps}
          </Typography>
        </Stack>
        
        <LinearProgress 
          variant="determinate" 
          value={progress}
          sx={{ 
            height: 8, 
            borderRadius: 4,
            bgcolor: 'grey.200',
            '& .MuiLinearProgress-bar': {
              borderRadius: 4
            }
          }}
        />
        
        {showLabels && stepLabels.length > 0 && (
          <Stack direction="row" justifyContent="space-between">
            {stepLabels.map((label, index) => (
              <Typography 
                key={index}
                variant="caption" 
                color={index < currentStep ? 'primary.main' : 'text.secondary'}
                fontWeight={index === currentStep ? 'medium' : 'normal'}
              >
                {label}
              </Typography>
            ))}
          </Stack>
        )}
      </Stack>
    </Box>
  );
}

// ----------------------------------------------------------------------

export function WorkflowValidationSummary({ validation = null }) {
  if (!validation || validation.isValid) return null;

  return (
    <Alert severity="warning" sx={{ mb: 2 }}>
      <Typography variant="subtitle2" gutterBottom>
        Cần khắc phục các vấn đề sau:
      </Typography>
      <Box component="ul" sx={{ m: 0, pl: 2 }}>
        {validation.errors.map((error, index) => (
          <Typography key={index} component="li" variant="body2">
            {error}
          </Typography>
        ))}
      </Box>
    </Alert>
  );
}
