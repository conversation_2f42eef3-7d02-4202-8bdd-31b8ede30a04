'use client';

import { useState, useCallback, useMemo } from 'react';

import {
  <PERSON>,
  Card,
  Stack,
  Button,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Chip,
  RadioGroup,
  FormControlLabel,
  Radio,
  Divider,
  Tabs,
  Tab,
  Alert,
  Switch,
  Tooltip
} from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { Label } from 'src/components/label';
import { PRODUCT_TYPE_LABELS, getBusinessFlow } from 'src/actions/mooly-chatbot/order-status-business-rules';


// ----------------------------------------------------------------------

export default function WorkflowTemplateDialog({
  open,
  onClose,
  onSubmit,
  templates,
  productType,
  loading = false
}) {
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [customData, setCustomData] = useState({
    name: '',
    description: '',
    is_active: true,
    is_default: false
  });
  const [compareMode, setCompareMode] = useState(false);
  const [selectedForCompare, setSelectedForCompare] = useState([]);

  // Categorize và filter templates
  const categorizedTemplates = useMemo(() => {
    const filtered = Object.entries(templates).filter(([key, template]) =>
      template.product_type === productType || template.product_type === 'all'
    );

    // Group by category hoặc complexity
    const categories = {
      recommended: [],
      standard: [],
      advanced: [],
      custom: []
    };

    filtered.forEach(([key, template]) => {
      const stageCount = template.stages?.length || 0;
      const hasAdvancedFeatures = template.stages?.some(s =>
        s.auto_transition || s.requires_payment || s.requires_inventory
      );

      if (template.product_type === productType && stageCount <= 5) {
        categories.recommended.push([key, template]);
      } else if (stageCount <= 7 && !hasAdvancedFeatures) {
        categories.standard.push([key, template]);
      } else if (hasAdvancedFeatures || stageCount > 7) {
        categories.advanced.push([key, template]);
      } else {
        categories.custom.push([key, template]);
      }
    });

    return categories;
  }, [templates, productType]);

  // Get business flow recommendation
  const businessFlowInfo = useMemo(() => {
    const flow = getBusinessFlow(productType);
    return {
      flow,
      recommendedStageCount: flow.length,
      description: `Quy trình chuẩn cho ${PRODUCT_TYPE_LABELS[productType]} có ${flow.length} bước chính`
    };
  }, [productType]);

  const handleSubmit = useCallback(async () => {
    if (!selectedTemplate) return;
    
    const result = await onSubmit(selectedTemplate, customData);
    if (result.success) {
      setSelectedTemplate('');
      setCustomData({ name: '', description: '' });
    }
  }, [selectedTemplate, customData, onSubmit]);

  const handleTemplateSelect = useCallback((templateKey) => {
    setSelectedTemplate(templateKey);
    const template = templates[templateKey];
    if (template && !customData.name) {
      setCustomData(prev => ({
        ...prev,
        name: template.name,
        description: template.description
      }));
    }
  }, [templates, customData.name]);

  const handleCustomDataChange = useCallback((field) => (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setCustomData(prev => ({ ...prev, [field]: value }));
  }, []);

  const renderTemplateCard = (templateKey, template, isRecommended = false) => (
    <Card
      key={templateKey}
      variant={selectedTemplate === templateKey ? 'elevation' : 'outlined'}
      sx={{
        p: 2,
        cursor: 'pointer',
        border: selectedTemplate === templateKey ? 2 : 1,
        borderColor: selectedTemplate === templateKey ? 'primary.main' : isRecommended ? 'warning.main' : 'divider',
        bgcolor: isRecommended ? 'warning.lighter' : 'background.paper',
        '&:hover': {
          borderColor: 'primary.main',
          bgcolor: 'action.hover'
        }
      }}
      onClick={() => handleTemplateSelect(templateKey)}
    >
      <Stack spacing={2}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Stack direction="row" alignItems="center" spacing={1}>
            {isRecommended && (
              <Iconify icon="solar:star-bold" sx={{ color: 'warning.main' }} />
            )}
            <Typography variant="h6">
              {template.name}
            </Typography>
          </Stack>
          <Radio
            checked={selectedTemplate === templateKey}
            onChange={() => handleTemplateSelect(templateKey)}
            size="small"
          />
        </Stack>

        <Typography variant="body2" color="text.secondary">
          {template.description}
        </Typography>

        <Stack direction="row" spacing={1} alignItems="center" flexWrap="wrap" useFlexGap>
          <Label color="info" size="small">
            {template.stages?.length || 0} bước
          </Label>
          <Label color="primary" size="small">
            {PRODUCT_TYPE_LABELS[template.product_type]}
          </Label>
          {isRecommended && (
            <Label color="warning" size="small">Đề xuất</Label>
          )}
          {template.stages?.some(s => s.auto_transition) && (
            <Label color="secondary" size="small">Tự động</Label>
          )}
          {template.stages?.some(s => s.requires_payment) && (
            <Label color="success" size="small">Thanh toán</Label>
          )}
        </Stack>

        <Divider />

        <Box>
          <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
            Các bước trong quy trình:
          </Typography>
          <Stack direction="row" spacing={0.5} flexWrap="wrap" useFlexGap>
            {template.stages?.map((stage, index) => (
              <Chip
                key={index}
                label={stage.name}
                size="small"
                color={stage.color || 'default'}
                variant="outlined"
              />
            ))}
          </Stack>
        </Box>
      </Stack>
    </Card>
  );

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Stack direction="row" alignItems="center" spacing={1}>
          <Iconify icon="solar:document-text-bold" />
          <Typography variant="h6">
            Chọn mẫu quy trình cho {PRODUCT_TYPE_LABELS[productType]}
          </Typography>
        </Stack>
      </DialogTitle>
      
      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          {/* Business Flow Info */}
          <Alert severity="info">
            <Typography variant="body2">
              <strong>{businessFlowInfo.description}</strong>
              <br />
              Quy trình chuẩn: {businessFlowInfo.flow.map((status, index, array) => (
                <span key={status}>
                  {status}
                  {index < array.length - 1 ? ' → ' : ''}
                </span>
              ))}
            </Typography>
          </Alert>

          {/* Template Categories */}
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 2 }}>
              Chọn mẫu quy trình
            </Typography>

            {/* Recommended Templates */}
            {categorizedTemplates.recommended.length > 0 && (
              <Box sx={{ mb: 3 }}>
                <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
                  <Iconify icon="solar:star-bold" color="warning.main" />
                  <Typography variant="subtitle2" color="warning.main">
                    Được đề xuất cho {PRODUCT_TYPE_LABELS[productType]}
                  </Typography>
                </Stack>
                <Grid container spacing={2}>
                  {categorizedTemplates.recommended.map(([templateKey, template]) => (
                    <Grid item xs={12} md={6} key={templateKey}>
                      {renderTemplateCard(templateKey, template, true)}
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}

            {/* Standard Templates */}
            {categorizedTemplates.standard.length > 0 && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" sx={{ mb: 2 }}>
                  Mẫu chuẩn
                </Typography>
                <Grid container spacing={2}>
                  {categorizedTemplates.standard.map(([templateKey, template]) => (
                    <Grid item xs={12} md={6} key={templateKey}>
                      {renderTemplateCard(templateKey, template)}
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}

            {/* Advanced Templates */}
            {categorizedTemplates.advanced.length > 0 && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" sx={{ mb: 2 }}>
                  Mẫu nâng cao
                </Typography>
                <Grid container spacing={2}>
                  {categorizedTemplates.advanced.map(([templateKey, template]) => (
                    <Grid item xs={12} md={6} key={templateKey}>
                      {renderTemplateCard(templateKey, template)}
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}

            {Object.values(categorizedTemplates).every(cat => cat.length === 0) && (
              <Box
                display="flex"
                flexDirection="column"
                alignItems="center"
                justifyContent="center"
                py={4}
                color="text.secondary"
              >
                <Iconify icon="solar:document-add-bold" width={48} sx={{ mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Không có mẫu nào
                </Typography>
                <Typography variant="body2" textAlign="center">
                  Hiện tại chưa có mẫu quy trình nào cho {PRODUCT_TYPE_LABELS[productType]}
                </Typography>
              </Box>
            )}
          </Box>
          
          {/* Custom Data */}
          {selectedTemplate && (
            <Box>
              <Typography variant="subtitle2" sx={{ mb: 2 }}>
                Tùy chỉnh thông tin
              </Typography>

              <Stack spacing={3}>
                <Stack direction="row" spacing={2}>
                  <TextField
                    label="Tên quy trình"
                    value={customData.name}
                    onChange={handleCustomDataChange('name')}
                    fullWidth
                    placeholder="Nhập tên tùy chỉnh cho quy trình"
                    helperText="Để trống để sử dụng tên mặc định từ mẫu"
                  />

                  <Box sx={{ minWidth: 200 }}>
                    <Stack spacing={1}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={customData.is_active}
                            onChange={handleCustomDataChange('is_active')}
                          />
                        }
                        label="Kích hoạt ngay"
                      />

                      <FormControlLabel
                        control={
                          <Switch
                            checked={customData.is_default}
                            onChange={handleCustomDataChange('is_default')}
                          />
                        }
                        label="Đặt làm mặc định"
                      />
                    </Stack>
                  </Box>
                </Stack>

                <TextField
                  label="Mô tả"
                  value={customData.description}
                  onChange={handleCustomDataChange('description')}
                  fullWidth
                  multiline
                  rows={3}
                  placeholder="Nhập mô tả tùy chỉnh cho quy trình"
                  helperText="Để trống để sử dụng mô tả mặc định từ mẫu"
                />
              </Stack>
            </Box>
          )}
          
          {/* Preview */}
          {selectedTemplate && templates[selectedTemplate] && (
            <Box>
              <Typography variant="subtitle2" sx={{ mb: 2 }}>
                Xem trước quy trình
              </Typography>
              
              <Card variant="outlined" sx={{ p: 2 }}>
                <Stack spacing={2}>
                  <Typography variant="body2" color="text.secondary">
                    Quy trình sẽ được tạo với {templates[selectedTemplate].stages?.length || 0} bước:
                  </Typography>
                  
                  <Stack spacing={1}>
                    {templates[selectedTemplate].stages?.map((stage, index) => (
                      <Stack key={index} direction="row" alignItems="center" spacing={2}>
                        <Typography variant="body2" color="text.secondary" sx={{ minWidth: 24 }}>
                          {index + 1}.
                        </Typography>
                        <Chip
                          label={stage.name}
                          size="small"
                          color={stage.color || 'default'}
                          variant="outlined"
                        />
                        {stage.is_start_stage && (
                          <Label color="info" size="small">Bắt đầu</Label>
                        )}
                        {stage.is_end_stage && (
                          <Label color="success" size="small">Kết thúc</Label>
                        )}
                        {stage.requires_payment && (
                          <Label color="warning" size="small">Thanh toán</Label>
                        )}
                      </Stack>
                    ))}
                  </Stack>
                </Stack>
              </Card>
            </Box>
          )}
        </Stack>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>Hủy</Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={!selectedTemplate || loading}
          startIcon={loading ? <Iconify icon="solar:loading-bold" /> : <Iconify icon="solar:add-circle-bold" />}
        >
          {loading ? 'Đang tạo...' : 'Tạo từ mẫu'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
